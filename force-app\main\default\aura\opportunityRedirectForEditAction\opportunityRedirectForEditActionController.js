({
    handleAppContext : function(component, event, helper) {
        const context = event.getParam("appContext");
        component.set("v.appContext", context);
        console.log("App context ricevuto dal LWC:", context);

        // Esegui il redirect solo dopo aver ricevuto il contesto
        helper.redirectIfNeeded(component);
    },

    doRedirect : function(component, event, helper) {
        // Non fare nulla qui: il redirect sarà gestito da handleAppContext
    }
})
