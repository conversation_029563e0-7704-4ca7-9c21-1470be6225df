public with sharing class AppContextController {
    @AuraEnabled(cacheable=true)
    public static String getCurrentAppLabel() {
        try {
            UserAppInfo userAppInfo = [
                SELECT Id, AppDefinitionId 
                FROM UserAppInfo 
                WHERE UserId = :UserInfo.getUserId() 
                LIMIT 1
            ];

            AppDefinition appDefinition = [
                SELECT DurableId, Label 
                FROM AppDefinition 
                WHERE DurableId = :userAppInfo.AppDefinitionId 
                LIMIT 1
            ];

            return appDefinition.Label;
        } catch (Exception e) {
            System.debug('Errore nel recupero app: ' + e.getMessage());
            return 'unknown';
        }
    }
}
