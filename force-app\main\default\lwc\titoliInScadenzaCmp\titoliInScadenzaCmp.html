<template>
    <lightning-card title="Titoli in scadenza" icon-name="standard:recent">
        <template lwc:if={showUnipolButton}>
            <lightning-button label="Gestione Stampe Unipol" slot="actions" data-fei-id="GESTIONE.STAMPE" data-society-id="SOC_1" onclick={handleStampe}></lightning-button>
        </template>
        <template lwc:if={showUniSaluteButton}>
            <lightning-button label="Gestione Stampe UniSalute" slot="actions" data-fei-id="GESTIONE.STAMPE" data-society-id="SOC_4" onclick={handleStampe}></lightning-button>
        </template>

        <template lwc:if={datatableRendered}>
            <lightning-datatable
                key-field="rowId"
                data={titoliInScadenzaData}
                columns={titoliInScadenzaColumn}
                onrowaction={handleRowAction}
                hide-checkbox-column
            ></lightning-datatable>  
        </template>  
    </lightning-card>  

    <template lwc:if={isFlowModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={toggleFlowModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">    
                    <lightning-flow 
                        flow-api-name="FEIQuickActionByAccount" 
                        flow-input-variables={flowInputs} 
                        onstatuschange={handleFlowStatusChange}>
                    </lightning-flow>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template lwc:if={isLWCModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={toggleLWCModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">
                    <p>FEI Container attivo per: {clickedAction}</p>
                    <c-fei-container
                        feiid={params.feiId}
                        fiscalcode={params.fiscalCode}
                        fei-request-payload={params.feiRequestPayload}
                        permission-set-name={params.permissionSetName}
                        society={params.society}
                        >
                    </c-fei-container>
                </div>
            </div>
        </section>
            
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template lwc:if={isInvioRemotoModalOpened}>
        <section role="dialog" class="slds-modal slds-fade-in-open slds-modal_medium sect" tabindex="-1">
            <div class="slds-modal__container container slds-p-around_x-large">
                <div class="titoloContainer">
                    <h2 class="titolo slds-float_left textColor">INVIA DOCUMENTO</h2>
                </div>  
                <div class="">
                    <lightning-tabset>
                        <lightning-tab label="INVIA PER E-MAIL">
                            <div class="mail-tab-container">
                                <div class="slds-grid slds-wrap slds-m-vertical_medium">
                                    <div class="slds-col slds-size_6-of-12 radioButt">
                                        <input type="radio" name="emailD" class="slds-m-right_small textColor" checked>email dispositiva
                                    </div>
                                    <div class="slds-col slds-size_6-of-12 radioButt">
                                        <input type="radio" name="emailA" class="slds-m-right_small textColor">email Anag Agenzia
                                    </div>
                                </div> 
                                <p style="font-weight: bold;" class="slds-m-top_small textColor">Attenzione: sarà necessario raccogliere la firma del contraente</p> <br>
                                <p class="textColor">In caso di invio a "email dispositiva". la Quietanza sarà inviata all'indirizzo email dispostitivo collegato a FEA e Area Riservata.</p> <br>
                                <p class="textColor">In caso di invio a email Anag Agenzia sarà invece inviata all'indirizzo presente su Anagrafica</p> <br>
                                <p class="corsivo textColor">I documenti verranno inoltrati il giorno successivo alla conferma</p>
                            </div>
                        </lightning-tab>
                        <lightning-tab label="POSTALIZZA">
                            <div class="posta-tab-container">
                                <p style="font-weight: bold;" class="textColor">Attenzione: sarà necessario raccogliere la firma del contraente</p> <br>
                                <p class="textColor">La Quietanza verrà postalizzata all'indirizzo configurato su Essig</p> <br>
                                <p class="corsivo textColor">*Il documento verrà postalizzato il giorno successivo alla conferma</p>
                            </div>
                        </lightning-tab>
                    </lightning-tabset>
                    <div class="picklist-container">
                        <label style="font-weight: 500;" for="slct" class="textColor">MOTIVO RISTAMPA</label>
                        <select class="select" name="pets" id="slct">
                        <option value="null"></option>
                        <option value="dog">Dog</option>
                        <option value="cat">Cat</option>
                        </select>
                    </div>

                    <div class="slds-grid slds-wrap slds-m-top_large">
                        <div class="slds-col slds-size_6-of-12">
                            <input class="buttonStyle annullaButton" type="button" value="Annulla" onclick={toggleInvioRemotoModal} /> 
                        </div>
                        <div class="slds-col slds-size_6-of-12">
                            <input class="buttonStyle textColor confermaButton slds-float_right" type="button" value="Conferma" disabled onclick={toggleInvioRemotoModal} click={handleClick}/>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <template lwc:if={showSpinner}>
        <lightning-spinner alternative-text={labelLoadingTitoli} size="large"></lightning-spinner> 
    </template>




</template>