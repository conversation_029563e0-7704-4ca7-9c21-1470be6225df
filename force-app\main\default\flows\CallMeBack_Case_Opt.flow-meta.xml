<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assegna la trattativa trovata e alla stessa assegna il Rating a &quot;Caldissima&quot;</description>
        <name>Assign_Container_Found</name>
        <label>Assign Container Found</label>
        <locationX>886</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Open_Container</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeToStatusFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_If_Update_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Customer</name>
        <label>Assign Customer</label>
        <locationX>1152</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>customerInput</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Folder_Id</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Journey_Step</name>
        <label>Assign Journey Step</label>
        <locationX>1722</locationX>
        <locationY>2546</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerOppJourneyStep</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Working_Expiry_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Name_to_New_Container</name>
        <label>Assign Name to New Container</label>
        <locationX>1194</locationX>
        <locationY>1514</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Container_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Name_to_Product</name>
        <label>Assign Name to Product</label>
        <locationX>1040</locationX>
        <locationY>2246</locationY>
        <assignmentItems>
            <assignToReference>productRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Product_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Container</name>
        <label>Assign Product Container</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product_Container</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>startingContainerNeeds</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product_Container.OverallAreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Product_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Found</name>
        <label>Assign Product Found</label>
        <locationX>182</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>productRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Product_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Opportunity</name>
        <label>Assign Product Opportunity</label>
        <locationX>1854</locationX>
        <locationY>1490</locationY>
        <assignmentItems>
            <assignToReference>productRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Quote_Product</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Quote_Product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Quote_Container</name>
        <label>Assign Quote Container</label>
        <locationX>1854</locationX>
        <locationY>1814</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Quote_Container</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Quote_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Quote_Record</name>
        <label>Assign Quote Record</label>
        <locationX>1854</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>quoteRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Quote</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>InsuranceQuoteRT.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_if_Is_Call_Me_Back_4_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Quote_Record_Status</name>
        <label>Assign Quote Record Status</label>
        <locationX>1942</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>quoteRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeToStatusFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Quote_Rating</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_response</name>
        <label>Assign Response</label>
        <locationX>1064</locationX>
        <locationY>4646</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>AssignNametoUnmatchedContainer</name>
        <label>Assign Name to Unmatched Container</label>
        <locationX>490</locationX>
        <locationY>1406</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>UpdateUnmatchedContainerName</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Amount_Update</name>
        <label>Container Amount Update</label>
        <locationX>1722</locationX>
        <locationY>2246</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Container_Journey_Step</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_TIC_Expiry_Update</name>
        <label>Container TIC Expiry Update</label>
        <locationX>932</locationX>
        <locationY>3446</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteTICDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Is_Container_Update_Needed</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Container_Working_Expiry_Update</name>
        <label>Container Working Expiry Update</label>
        <locationX>932</locationX>
        <locationY>3146</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteExpiryDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TIC_Expiry_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Journey_Step</name>
        <label>Copy 1 of Assign Journey Step</label>
        <locationX>50</locationX>
        <locationY>1622</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerOppJourneyStep</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Product_Opp_Updated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_New_Container</name>
        <label>Initialize New Container</label>
        <locationX>1194</locationX>
        <locationY>1190</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Assegnato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Preventivatore digitale Unica</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Canale Digitale</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputActivity.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputActivity.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AgencySelectedByLocator__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>selectedByLocatorPickVal</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignmentCounter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeToStatusFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputCip</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Product_Record</name>
        <label>Initialize Product Record</label>
        <locationX>1040</locationX>
        <locationY>1922</locationY>
        <assignmentItems>
            <assignToReference>productRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.StageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Preventivatore digitale Unica</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>PU</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.LeadSource</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Preventivatore digitale Unica</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Parent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.AreaOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Unmatched_Container</name>
        <label>Initialize Unmatched Container</label>
        <locationX>490</locationX>
        <locationY>1082</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Assegnato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Preventivatore digitale Unica</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Canale Digitale</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AgencySelectedByLocator__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>selectedByLocatorPickVal</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputActivity.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputActivity.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignmentCounter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>eventTypeToStatusFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Salespoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Service_Territory.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Unmatched_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>No_Exist_Quote_Response</name>
        <label>No Exist Quote Response</label>
        <locationX>1458</locationX>
        <locationY>866</locationY>
        <assignmentItems>
            <assignToReference>flowResponse</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>flowResponseFormula</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <constants>
        <name>defaultRecordName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>TEMP</stringValue>
        </value>
    </constants>
    <constants>
        <name>EVENT_TYPE_RICHIESTA_CONTATTO</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RICHIESTA_CONTATTO</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Check_Container_Amount</name>
        <label>Check Container Amount</label>
        <locationX>1854</locationX>
        <locationY>2138</locationY>
        <defaultConnector>
            <targetReference>Check_Container_Journey_Step</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Amount</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.Amount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>containerAmount</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Amount_Update</targetReference>
            </connector>
            <label>Update Amount</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Container_Journey_Step</name>
        <label>Check Container Journey Step</label>
        <locationX>1854</locationX>
        <locationY>2438</locationY>
        <defaultConnector>
            <targetReference>Check_Working_Expiry_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_Journey_Step</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerOppJourneyStep</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>containerRecord.JourneyStep__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Journey_Step</targetReference>
            </connector>
            <label>Update Journey Step</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Debug_Flag</name>
        <label>Check Debug Flag</label>
        <locationX>1064</locationX>
        <locationY>4046</locationY>
        <defaultConnector>
            <targetReference>Check_is_CallMeBack</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Standard Mode</defaultConnectorLabel>
        <rules>
            <name>Debug_Mode</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.FlowSettings__c.DebugMode__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Debug_Driven_Error</targetReference>
            </connector>
            <label>Debug Mode</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Folder_Id</name>
        <label>Check Folder Id</label>
        <locationX>1064</locationX>
        <locationY>542</locationY>
        <defaultConnector>
            <targetReference>Get_Quote</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Null</defaultConnectorLabel>
        <rules>
            <name>Null</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>contractId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>contractId</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <conditions>
                <leftValueReference>contractId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>null</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>contractId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>&quot;&quot;</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>contractId</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Product</targetReference>
            </connector>
            <label>Null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Container_Found</name>
        <label>Check if Container Found</label>
        <locationX>1040</locationX>
        <locationY>1082</locationY>
        <defaultConnector>
            <targetReference>Initialize_New_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Container_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Container</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Container_Found</targetReference>
            </connector>
            <label>Container Found</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Is_Call_Me_Back_4_Quote</name>
        <label>Check if Is Call Me Back 4 Quote</label>
        <locationX>1854</locationX>
        <locationY>974</locationY>
        <defaultConnector>
            <targetReference>Assign_Quote_Record_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CallMeBack</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>eventTypeCallMeBackFormula</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Quote_Rating</targetReference>
            </connector>
            <label>CallMeBack</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Product_Found</name>
        <label>Check If Product Found</label>
        <locationX>473</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>Check_Product_Matching_Setting</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Product_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Product</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Product_Found</targetReference>
            </connector>
            <label>Product Found</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Quote_Exist</name>
        <label>Check If Quote Exist</label>
        <locationX>1656</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>Assign_Quote_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Exist</defaultConnectorLabel>
        <rules>
            <name>Not_Exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Quote</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>No_Exist_Quote_Response</targetReference>
            </connector>
            <label>Not Exist</label>
        </rules>
    </decisions>
    <decisions>
        <description>Verifica se c&apos;è la necessità di aggiornare il Rating sulla trattativa trovata. In particolare se il Rating è già &quot;Caldissima&quot; si evita di fare l&apos;aggiornamneto</description>
        <name>Check_If_Update_Container</name>
        <label>Check If Update Container</label>
        <locationX>886</locationX>
        <locationY>1298</locationY>
        <defaultConnector>
            <targetReference>Initialize_Product_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Needed</defaultConnectorLabel>
        <rules>
            <name>Need_Update</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Container.Rating__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Caldissima</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update</targetReference>
            </connector>
            <label>Need Update</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if EventType is Richiesta Contatto</description>
        <name>Check_is_CallMeBack</name>
        <label>Check is CallMeBack</label>
        <locationX>1064</locationX>
        <locationY>4346</locationY>
        <defaultConnector>
            <targetReference>Assign_response</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>false</defaultConnectorLabel>
        <rules>
            <name>true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>eventTypeCallMeBackFormula</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_CallMeBack_Event_History</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Matching_Setting</name>
        <label>Check Product Matching Setting</label>
        <locationX>765</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>Get_Open_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Matching Enabled</defaultConnectorLabel>
        <rules>
            <name>Matching_Disabled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>oppConfig.AutomaticMatching__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Service_Territory</targetReference>
            </connector>
            <label>Matching Disabled</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_TIC_Expiry_Date</name>
        <label>Check TIC Expiry Date</label>
        <locationX>1064</locationX>
        <locationY>3338</locationY>
        <defaultConnector>
            <targetReference>Is_Container_Update_Needed</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Update_TIC_Expiry</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>containerRecord.TakenInChargeSLAExpiryDate__c</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>quoteTICDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_TIC_Expiry_Update</targetReference>
            </connector>
            <label>Update TIC Expiry</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Working_Expiry_Date</name>
        <label>Check Working Expiry Date</label>
        <locationX>1064</locationX>
        <locationY>3038</locationY>
        <defaultConnector>
            <targetReference>Check_TIC_Expiry_Date</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Update</defaultConnectorLabel>
        <rules>
            <name>Update_Working_Expiry</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>containerRecord.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>EqualTo</operator>
            </conditions>
            <conditions>
                <leftValueReference>containerRecord.WorkingSLAExpiryDate__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>quoteExpiryDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Container_Working_Expiry_Update</targetReference>
            </connector>
            <label>Update Working Expiry</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Check_Container_Journey_Step</name>
        <label>Copy 1 of Check Container Journey Step</label>
        <locationX>182</locationX>
        <locationY>1514</locationY>
        <defaultConnector>
            <targetReference>Get_Product_Opp_Updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Update_Journey_Step</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerOppJourneyStep</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>containerRecord.JourneyStep__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Assign_Journey_Step</targetReference>
            </connector>
            <label>Copy 1 of Update Journey Step</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Container_Update_Needed</name>
        <label>Is Container Update Needed</label>
        <locationX>1064</locationX>
        <locationY>3638</locationY>
        <defaultConnector>
            <targetReference>Get_Container_Record_Updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Update</defaultConnectorLabel>
        <rules>
            <name>Update_Needed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>containerUpdateNeeded</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container_Record</targetReference>
            </connector>
            <label>Update Needed</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Valid_Customer</name>
        <label>is Valid Customer</label>
        <locationX>1064</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Get_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>customerInput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Folder_Id</targetReference>
            </connector>
            <label>Valid Customer</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>caseSubjectFormula</name>
        <dataType>String</dataType>
        <expression>&apos;Autore:&apos; + {!customerInput.Name}</expression>
    </formulas>
    <formulas>
        <name>containerNeedFormula</name>
        <dataType>String</dataType>
        <expression>IF(ISBLANK({!startingContainerNeeds}), {!containerNeeds}, {!startingContainerNeeds} + &apos;;&apos; + {!containerNeeds})</expression>
    </formulas>
    <formulas>
        <name>defaultCloseDate</name>
        <dataType>Date</dataType>
        <expression>TODAY() + 60</expression>
    </formulas>
    <formulas>
        <name>eventTypeCallMeBackFormula</name>
        <dataType>Boolean</dataType>
        <expression>IF({!eventType} == {!EVENT_TYPE_RICHIESTA_CONTATTO}, true, false)</expression>
    </formulas>
    <formulas>
        <name>eventTypeToStatusFormula</name>
        <dataType>String</dataType>
        <expression>CASE({!eventType} , &apos;SALVA_PREVENTIVO&apos;,  &apos;Salvataggio preventivo&apos;, &apos;POSIZIONE_ABBANDONATA_DA_CARRELLO&apos;, &apos;Acquisto non concluso&apos;, &apos;MANCATO_ACQUISTO_CARRELLO&apos;, &apos;Acquisto non concluso&apos;, &apos;PAGAMENTO_KO&apos;, &apos;Acquisto non concluso&apos;, &apos;&apos;)</expression>
    </formulas>
    <interviewLabel>CallMeBack-Case-Opt {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CallMeBack-Case-Opt</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Assignment_Event_Creazione_Trattativa</name>
        <label>Create Assignment Event - Creazione Trattativa</label>
        <locationX>1194</locationX>
        <locationY>1730</locationY>
        <connector>
            <targetReference>Initialize_Product_Record</targetReference>
        </connector>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Creazione Trattativa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_CallMeBack_Event_History</name>
        <label>Create CallMeBack Event History</label>
        <locationX>932</locationX>
        <locationY>4454</locationY>
        <connector>
            <targetReference>Assign_response</targetReference>
        </connector>
        <inputAssignments>
            <field>Executor__c</field>
            <value>
                <elementReference>caseSubjectFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Call Me Back</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Container</name>
        <label>Create Container</label>
        <locationX>1194</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>Retrieve_New_Container_Name</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Product</name>
        <label>Create Product</label>
        <locationX>1040</locationX>
        <locationY>2030</locationY>
        <connector>
            <targetReference>Retrieve_Product_Name</targetReference>
        </connector>
        <inputReference>productRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Unmatched_Assignment_Event_Creazione_Trattativa</name>
        <label>Create Unmatched Assignment Event - Creazione Trattativa</label>
        <locationX>490</locationX>
        <locationY>1622</locationY>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Initialize_Product_Record</targetReference>
        </connector>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Creazione Trattativa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Unmatched_Container</name>
        <label>Create Unmatched Container</label>
        <locationX>490</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Retrieve_Unmatched_Container_Name</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Debug_Driven_Error</name>
        <label>Debug Driven Error</label>
        <locationX>932</locationX>
        <locationY>4154</locationY>
        <connector>
            <targetReference>Check_is_CallMeBack</targetReference>
        </connector>
        <inputAssignments>
            <field>CloseDate</field>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
        </inputAssignments>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Container_Record_Updated</name>
        <label>Get Container Record Updated</label>
        <locationX>1064</locationX>
        <locationY>3938</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Debug_Flag</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>1152</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Verifica se è presente una trattativa aperta</description>
        <name>Get_Open_Container</name>
        <label>Get Open Container</label>
        <locationX>1040</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_Container_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product</name>
        <label>Get Product</label>
        <locationX>473</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_If_Product_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>PU</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Container</name>
        <label>Get Product Container</label>
        <locationX>182</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Product_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Product.Parent__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Opp_Updated</name>
        <label>Get Product Opp Updated</label>
        <locationX>473</locationX>
        <locationY>2630</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Evaluate_TIC_Exp</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Quote</name>
        <label>Get Quote</label>
        <locationX>1656</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_If_Quote_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FolderId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>contractId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Quote_Container</name>
        <label>Get Quote Container</label>
        <locationX>1854</locationX>
        <locationY>1706</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Quote_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Product.Parent__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Quote_From_Opp</name>
        <label>Get Quote From Opp</label>
        <locationX>182</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Re_Evaluate_Opp</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Product_Container.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Quote_Product</name>
        <label>Get Quote Product</label>
        <locationX>1854</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Product_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote.OpportunityId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Service_Territory</name>
        <label>Get Service Territory</label>
        <locationX>490</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Initialize_Unmatched_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Tipo__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Agenziale</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceTerritory</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update</name>
        <label>Update Container Rating</label>
        <locationX>754</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Initialize_Product_Record</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Container_Name</name>
        <label>Update Container Name</label>
        <locationX>1194</locationX>
        <locationY>1622</locationY>
        <connector>
            <targetReference>Create_Assignment_Event_Creazione_Trattativa</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Container_Record</name>
        <label>Update Container Record</label>
        <locationX>932</locationX>
        <locationY>3746</locationY>
        <connector>
            <targetReference>Get_Container_Record_Updated</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>HasCallMeBack__c</field>
            <value>
                <elementReference>eventTypeCallMeBackFormula</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>JourneyStep__c</field>
            <value>
                <elementReference>containerRecord.JourneyStep__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rating__c</field>
            <value>
                <stringValue>Caldissima</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>TakenInChargeSLAExpiryDate__c</field>
            <value>
                <elementReference>containerRecord.TakenInChargeSLAExpiryDate__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>WorkingSLAExpiryDate__c</field>
            <value>
                <elementReference>containerRecord.WorkingSLAExpiryDate__c</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Product_Container</name>
        <label>Update Product Container</label>
        <locationX>182</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Get_Quote_From_Opp</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Product_Name</name>
        <label>Update Product Name</label>
        <locationX>1040</locationX>
        <locationY>2354</locationY>
        <connector>
            <targetReference>Get_Product_Opp_Updated</targetReference>
        </connector>
        <inputReference>productRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Quote_Container</name>
        <label>Update Quote Container</label>
        <locationX>1854</locationX>
        <locationY>1922</locationY>
        <connector>
            <targetReference>Evaluate_Product</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Quote_Product</name>
        <label>Update Quote Product</label>
        <locationX>1854</locationX>
        <locationY>1598</locationY>
        <connector>
            <targetReference>Get_Quote_Container</targetReference>
        </connector>
        <inputReference>productRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Quote_Rating</name>
        <label>Update Quote Rating</label>
        <locationX>1854</locationX>
        <locationY>1274</locationY>
        <connector>
            <targetReference>Get_Quote_Product</targetReference>
        </connector>
        <inputReference>quoteRecord</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateUnmatchedContainerName</name>
        <label>Update Unmatched Container Name</label>
        <locationX>490</locationX>
        <locationY>1514</locationY>
        <connector>
            <targetReference>Create_Unmatched_Assignment_Event_Creazione_Trattativa</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordUpdates>
    <start>
        <locationX>938</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_Valid_Customer</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Evaluate_Product</name>
        <label>Evaluate Product</label>
        <locationX>1854</locationX>
        <locationY>2030</locationY>
        <connector>
            <targetReference>Check_Container_Amount</targetReference>
        </connector>
        <flowName>Quote_Opportunity_Evaluation_Opt</flowName>
        <inputAssignments>
            <name>opportunityParent</name>
            <value>
                <elementReference>Get_Quote_Product</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>targetQuote</name>
            <value>
                <elementReference>quoteRecord</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerOppJourneyStep</assignToReference>
            <name>containerOppJourneyStep</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>containerNeeds</assignToReference>
            <name>overallAreasOfNeed</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>quoteTICDate</assignToReference>
            <name>quoteTICExpiryDate</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>quoteExpiryDate</assignToReference>
            <name>quoteWorkingExpiryDate</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>containerAmount</assignToReference>
            <name>totalAmount</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Evaluate_TIC_Exp</name>
        <label>Evaluate TIC Exp</label>
        <locationX>473</locationX>
        <locationY>2738</locationY>
        <connector>
            <targetReference>Evaluate_Working_Exp</targetReference>
        </connector>
        <flowName>Engine_SLAExpiryDateCalculation</flowName>
        <inputAssignments>
            <name>CategoryName</name>
            <value>
                <stringValue>TakenInCharge</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>OpportunityRecord</name>
            <value>
                <elementReference>Get_Product_Opp_Updated</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>quoteTICDate</assignToReference>
            <name>expiryDate</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Evaluate_Working_Exp</name>
        <label>Evaluate Working Exp</label>
        <locationX>473</locationX>
        <locationY>2846</locationY>
        <connector>
            <targetReference>Check_Working_Expiry_Date</targetReference>
        </connector>
        <flowName>Engine_SLAExpiryDateCalculation</flowName>
        <inputAssignments>
            <name>CategoryName</name>
            <value>
                <stringValue>Working</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>OpportunityRecord</name>
            <value>
                <elementReference>Get_Product_Opp_Updated</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>quoteExpiryDate</assignToReference>
            <name>expiryDate</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Re_Evaluate_Opp</name>
        <label>Re Evaluate Opp</label>
        <locationX>182</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Copy_1_of_Check_Container_Journey_Step</targetReference>
        </connector>
        <flowName>Opportunity_Re_evalutation</flowName>
        <inputAssignments>
            <name>targetQuotes</name>
            <value>
                <elementReference>Get_Quote_From_Opp</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerOppJourneyStep</assignToReference>
            <name>contanierOppContactPoint</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Retrieve_New_Container_Name</name>
        <label>Retrieve New Container Name</label>
        <locationX>1194</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Assign_Name_to_New_Container</targetReference>
        </connector>
        <flowName>ST_Opportunity_Name_Assignment</flowName>
        <inputAssignments>
            <name>opportunityId</name>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerName</assignToReference>
            <name>outputName</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Retrieve_Product_Name</name>
        <label>Retrieve Product Name</label>
        <locationX>1040</locationX>
        <locationY>2138</locationY>
        <connector>
            <targetReference>Assign_Name_to_Product</targetReference>
        </connector>
        <flowName>ST_Opportunity_Name_Assignment</flowName>
        <inputAssignments>
            <name>opportunityId</name>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>productName</assignToReference>
            <name>outputName</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Retrieve_Unmatched_Container_Name</name>
        <label>Retrieve Unmatched Container Name</label>
        <locationX>490</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>AssignNametoUnmatchedContainer</targetReference>
        </connector>
        <flowName>ST_Opportunity_Name_Assignment</flowName>
        <inputAssignments>
            <name>opportunityId</name>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerName</assignToReference>
            <name>outputName</name>
        </outputAssignments>
    </subflows>
    <textTemplates>
        <name>flowResponseFormula</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{
	&quot;caseId&quot; : &quot;{!caseId}&quot;,
	&quot;opportunityId&quot;: &quot;{!containerRecord.Id}&quot;,
	&quot;productId&quot;: &quot;{!productRecord.Id}&quot;,
	&quot;quoteId&quot;: &quot;{!quoteRecord.Id}&quot;
}</text>
    </textTemplates>
    <variables>
        <name>actualContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>agencyId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CallMeBackCaseRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>caseContainer</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>caseId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ContactHistoryEventRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>containerAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>containerName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerOppJourneyStep</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>containerUpdateNeeded</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>contractId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>customerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>customerInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <description>Da rimuovere e sostituire nel Create Record del Case con la formula caseDescriptio</description>
        <name>Description</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Prova ************</stringValue>
        </value>
    </variables>
    <variables>
        <name>errorPoint</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>eventType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>flowError</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>flowResponse</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>inputActivity</name>
        <apexClass>RequestActivity</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputCip</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>InsuranceQuoteRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>OmnicanaleOppRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>oppConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityConfiguration__mdt</objectType>
    </variables>
    <variables>
        <description>Da rimuovere e sostituire nel CreateRecord del case con inputActivity.numeroRiconttato</description>
        <name>Phone</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>+393282872756</stringValue>
        </value>
    </variables>
    <variables>
        <name>ProdottoOppRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>productName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>productRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>quoteExpiryDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>quoteTICDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>selectedByLocatorPickVal</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>startingContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
