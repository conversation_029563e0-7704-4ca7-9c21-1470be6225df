/**
 * @File Name         : OpportunityManagementWS.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 04-30-2025
 * @Last Modified By  : <EMAIL>
@cicd_tests OpportunityManagementWSTest
**/
@RestResource(urlMapping='/v1/manageOpportunity')
global without sharing class OpportunityManagementWS {
  private static final String LOG_PREFIX = '[OpportunityManagementWS]';
  @HttpPost
  global static void manageOpportunity() {
    RestRequest req = RestContext.request;
    RestResponse res = RestContext.response;

    Savepoint sp;
    String corrId = 'N/A';

    try {
      RequestWrapper input = (RequestWrapper) JSON.deserialize(
        req.requestBody.toString(),
        RequestWrapper.class
      );
      System.debug('flowInput: ' + input);
      // derive a correlation id from request payload if present
      try {
        corrId = (input != null &&
          input.id != null)
          ? input.id
          : ((input != null &&
              input.data != null &&
              input.data.eventId != null)
              ? input.data.eventId
              : 'N/A');
      } catch (Exception ignore) {
        corrId = 'N/A';
      }
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' ENTER manageOpportunity httpMethod=' +
          req.httpMethod +
          ' uri=' +
          req.requestURI +
          ' corrId=' +
          corrId
      );
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' Payload summary corrId=' +
          corrId +
          ' channelCode=' +
          (input != null &&
            input.data != null
            ? input.data.channelCode
            : null) +
          ' eventType=' +
          (input != null && input.data != null ? input.data.eventType : null) +
          ' eventSource=' +
          (input != null &&
            input.data != null
            ? input.data.eventSource
            : null) +
          ' objectType=' +
          (input != null && input.data != null ? input.data.objectType : null) +
          ' companyCode=' +
          (input != null && input.data != null ? input.data.companyCode : null)
      );
      // Boolean isPerson = input.data.contractor.type == 'Individual';

      // Account customer = getCustomer(input, isPerson, res);
      Account customer = getCustomer(input, res);
      if (customer == null) {
        System.debug(
          LoggingLevel.WARN,
          LOG_PREFIX + ' Customer resolution failed corrId=' + corrId
        );
        return;
      }

      Account agency = getAgency(input, res);
      if (agency == null) {
        System.debug(
          LoggingLevel.WARN,
          LOG_PREFIX + ' Agency resolution failed corrId=' + corrId
        );
        return;
      }

      OpportunityConfiguration__mdt oppConfig = getOpportunityConfig(
        input,
        res
      );
      if (oppConfig == null) {
        System.debug(
          LoggingLevel.WARN,
          LOG_PREFIX +
            ' Opportunity config not found corrId=' +
            corrId +
            ' channelCode=' +
            (input != null &&
              input.data != null
              ? input.data.channelCode
              : null)
        );
        return;
      }
      system.debug('%%Francesca R. oppConfig: ' + oppConfig);
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' OppConfig loaded corrId=' +
          corrId +
          ' devName=' +
          oppConfig.DeveloperName +
          ' autoOpen=' +
          oppConfig.AutomaticOpening__c +
          ' autoClose=' +
          oppConfig.AutomaticClosing__c +
          ' merge=' +
          oppConfig.Merge__c +
          ' split=' +
          oppConfig.Split__c
      );
      ProductConfiguration__mdt prodConfig;
      if (input.data.channelCode != 'APP' && input.data.eventSource != null) {
        //Francesca R.
        prodConfig = getProductConfig(input, res);
        system.debug('%%Francesca R. prodConfig: ' + prodConfig);
        System.debug(
          LoggingLevel.INFO,
          LOG_PREFIX +
            ' ProdConfig loaded corrId=' +
            corrId +
            ' domain=' +
            (prodConfig != null ? prodConfig.DomainType__c : null) +
            ' product=' +
            (prodConfig != null ? prodConfig.ProductCode__c : null)
        );
        if (prodConfig == null) {
          System.debug(
            LoggingLevel.WARN,
            LOG_PREFIX +
              ' Product config not found corrId=' +
              corrId +
              ' eventSource=' +
              (input != null &&
                input.data != null
                ? input.data.eventSource
                : null)
          );
          return;
        }
      }

      OpportunityWSWrapper flowInput = input.data.payload;
      RequestActivity flowInputActivity = input.data.activity;
      if (flowInput != null) {
        flowInput.selectedByLocator = input.data.salesNetwork.selectedByLocator;
      }
      //flowInput.cip = input.data.salesNetwork.cip;
      //System.debug('flowInput: ' +JSON.serialize(flowInput));
      system.debug('%%Francesca R. customerId: ' + customer.Id);
      system.debug('%%Francesca R. agencyId: ' + agency.Id);
      system.debug('%%Francesca R. flowInput: ' + flowInput);
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' Context corrId=' +
          corrId +
          ' customerId=' +
          customer.Id +
          ' agencyId=' +
          agency.Id +
          ' selectedByLocator=' +
          (flowInput != null
            ? String.valueOf(flowInput.selectedByLocator)
            : null)
      );

      system.debug('@GL objectType: ' + input.data.objectType);
      String contractId = input.data.contract.id;
      if (contractId == null) {
        system.debug('%%MG contract id vuoto');
      } else {
        system.debug('%%MG contractId: ' + contractId);
      }
      Map<String, Object> inputVariables = new Map<String, Object>();
      inputVariables.put('eventType', input.data.eventType);
      inputVariables.put('customerId', customer.Id);
      inputVariables.put('customer', customer);
      inputVariables.put('agencyId', agency.Id);
      inputVariables.put('agency', agency);
      inputVariables.put('input', flowInput);
      inputVariables.put('contractId', contractId); //Folder Id
      //Gallo da decommentare per call me back
      inputVariables.put('inputActivity', flowInputActivity);
      inputVariables.put('objectType', input.data.objectType);
      inputVariables.put('oppConfig', oppConfig);
      if (
        input.data.channelCode != 'APP' &&
        input.data.eventSource != null &&
        input.data.eventSource != 'Pega'
      ) {
        //Francesca R.
        inputVariables.put('prodConfig', prodConfig);
      }
      if (String.isNotBlank(input.data.contract.intermediaryId)) {
        List<User> userList = [
          SELECT Id, FiscalCode__c
          FROM User
          WHERE FiscalCode__c = :input.data.contract.intermediaryId
        ];
        if (!userList.isEmpty()) {
          inputVariables.put('intermediaryId', userList[0].Id);
        }
      }

      sp = Database.setSavepoint();
      //Flow.Interview flowExecution = Flow.Interview.createInterview(oppConfig.MatchMergeFlowId__c, inputVariables);
      // Alessio Ilari edit

      List<FinServ__AccountAccountRelation__c> rels = [
        SELECT FinServ__Account__c, FinServ__RelatedAccount__c
        FROM FinServ__AccountAccountRelation__c
        WHERE
          FinServ__Account__c = :customer.Id
          AND FinServ__RelatedAccount__c = :agency.Id
          AND RecordType.DeveloperName = 'AccountAgency'
        LIMIT 1
      ];

      if (rels.isEmpty()) {
        Database.rollback(sp);
        System.debug(
          LoggingLevel.INFO,
          LOG_PREFIX +
            ' No Account-Agency relation corrId=' +
            corrId +
            ' -> enqueue async integration and return 202'
        );

        List<AccountDetails__c> details = [
          SELECT SourceSystemIdentifier__c
          FROM AccountDetails__c
          WHERE Account__c = :customer.Id
          LIMIT 1
        ];
        String ciu = details.isEmpty()
          ? null
          : details[0].SourceSystemIdentifier__c;

        Date today = Date.today();
        String mm = today.month() < 10
          ? '0' + today.month()
          : String.valueOf(today.month());
        String dd = today.day() < 10
          ? '0' + today.day()
          : String.valueOf(today.day());
        String isoDate = today.year() + '-' + mm + '-' + dd;

        Map<String, Object> extBody = new Map<String, Object>{
          'flagClienteTop' => false,
          'dataClienteTop' => null,
          'flagAdesioneFEA' => false,
          'flagProprietaContattiFea' => false,
          'codiceSubagenzia' => null,
          'codiceAgenziaPrevalente' => agency.AgencyCode__c,
          'flagAutorizzazioneCauzione' => false,
          'dataCessazioneCliente' => null,
          'statoSoggetto' => 'PO',
          'codiceSoggettoCanale' => null,
          'dataInizioEffetto' => isoDate,
          'dataFineEffetto' => null,
          'idDatiAnagraficiAgenzia' => null,
          'compagnia' => oppConfig.AllowedCompanies__c,
          'codiceCanale' => 'AGE',
          'ciu' => ciu,
          'codiceAgenzia' => agency.AgencyCode__c,
          'userId' => UserInfo.getUserId(),
          'username' => UserInfo.getFirstName() +
          ' ' +
          UserInfo.getLastName()
        };

        String bodyJson = JSON.serialize(extBody);

        System.enqueueJob(
          new AsyncCalloutJob('aggiornamentoDatiAnagraficiCanale', bodyJson)
        );

        setResponse(
          res,
          202,
          null,
          'Request accepted and processing asynchronously'
        );
        System.debug(
          LoggingLevel.INFO,
          LOG_PREFIX + ' Response 202 accepted corrId=' + corrId
        );
        return;
      }

      inputVariables.put('customerId', rels[0].FinServ__Account__c);
      inputVariables.put('agencyId', rels[0].FinServ__RelatedAccount__c);

      Flow.Interview flowExecution = Flow.Interview.createInterview(
        oppConfig.MatchMergeFlowId__c,
        inputVariables
      );
      System.debug('@MM inputFlow: ' + JSON.serialize(inputVariables));
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' Starting Flow corrId=' +
          corrId +
          ' flow=' +
          oppConfig.MatchMergeFlowId__c
      );
      flowExecution.start();

      String flowResponse = (String) flowExecution.getVariableValue(
        'flowResponse'
      );
      String flowError = (String) flowExecution.getVariableValue('flowError');

      system.debug('%%Francesca R. flowError: ' + flowError);
      system.debug('%%Francesca R. flowResponse: ' + flowResponse);
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' Flow completed corrId=' +
          corrId +
          ' hasError=' +
          (flowError != null)
      );

      if (flowError != null) {
        Database.rollback(sp);
        FlowException ex = new FlowException();
        ex.setMessage(
          (oppConfig != null ? oppConfig.MatchMergeFlowId__c : null) +
            ' : ' +
            flowError
        );
        throw ex;
      }

      if(flowResponse == null) {
        Database.rollback(sp);
        FlowException ex = new FlowException();
        ex.setMessage(
          (oppConfig != null ? oppConfig.MatchMergeFlowId__c : null) +
            ' : ' +
            'No response from flow'
        );
        throw ex;
      }

      ResponseCorrelationWrapper flowResponseData = (ResponseCorrelationWrapper) JSON.deserialize(
        flowResponse,
        ResponseCorrelationWrapper.class
      );
      System.debug('flowResponseData: ' + flowResponseData);
      setResponse(res, 200, applyCaseData(flowResponseData, input), null);

      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX + ' Response 200 OK corrId=' + corrId
      );
    } catch (Exception ex) {
      setResponse(res, 400, null, ex.getMessage());
      System.debug(
        LoggingLevel.ERROR,
        LOG_PREFIX +
          ' Exception corrId=' +
          corrId +
          ' type=' +
          ex.getTypeName() +
          ' message=' +
          ex.getMessage()
      );
      try {
        System.debug(
          LoggingLevel.ERROR,
          LOG_PREFIX + ' StackTrace=' + ex.getStackTraceString()
        );
      } catch (Exception ignore) {
      }
      logError(ex, req, res);
    }
  }

  private static ResponseCorrelationWrapper applyCaseData(
    ResponseCorrelationWrapper flowResponseData,
    RequestWrapper inputData
  ) {

    if(String.isBlank(flowResponseData.opportunityId)) {
      System.debug('No opportunityId from flow, skip case creation');
      return flowResponseData;
    }
    
    CaseCreationService.Context context = new CaseCreationService.Context();
    context.opportunityId = flowResponseData.opportunityId;
    // You can drive via objectType/eventType mapping...
    context.objectType = inputData.data.objectType; //data.objectType
    context.eventType = inputData.data.eventType; //data.eventType
    context.areaOfNeed = inputData.data.activity.areaOfNeed; //data.activity.areaOfNeed
    context.stageName = inputData.data.activity.stageName; //data.activity.stageName
    context.timeSlot = inputData.data.activity.timeSlot; //data.activity.timeSlot
    context.notes = inputData.data.activity.notes; //data.activity.notes
    context.codDomainActivity = inputData.data.activity.codDomainActivity; //data.activity.codDomainActivity
    context.numeroRicontatto = inputData.data.activity.numeroRicontatto; //data.activity.numeroRicontatto

    flowResponseData.caseContext = CaseCreationService.createCases(context);
    System.debug('caseService: ' + flowResponseData.caseContext);

    return flowResponseData;
  }

  private static Account getCustomer(RequestWrapper input, RestResponse res) {
    List<Account> customerList = new List<Account>();

    System.debug(
      LoggingLevel.INFO,
      LOG_PREFIX +
        ' getCustomer by ExternalId contractorId=' +
        (input != null &&
          input.data != null &&
          input.data.contractor != null
          ? input.data.contractor.id
          : null)
    );
    if (Schema.sObjectType.Account.isAccessible()) {
      customerList = [
        SELECT
          Id,
          Name,
          IsPersonAccount,
          FirstName,
          LastName,
          Email__c,
          Phone,
          PersonBirthdate
        FROM Account
        WHERE ExternalId__c = :input.data.contractor.id
        LIMIT 1
      ];
    } else {
      setResponse(
        res,
        403,
        null,
        'Insufficient permissions to access Account object'
      );
      System.debug(LoggingLevel.WARN, LOG_PREFIX + ' No FLS to read Account');
      return null;
    }

    if (customerList.isEmpty()) {
      setResponse(res, 404, null, 'Customer not found');
      System.debug(
        LoggingLevel.WARN,
        LOG_PREFIX +
          ' Customer not found contractorId=' +
          (input != null &&
            input.data != null &&
            input.data.contractor != null
            ? input.data.contractor.id
            : null)
      );
      return null;
    } else {
      enrichCustomer(
        input,
        customerList.get(0),
        customerList.get(0).IsPersonAccount
      );
      System.debug('Customer found: ' + customerList.get(0).Id);
    }

    return customerList.get(0);
  }

  private static void enrichCustomer(
    RequestWrapper input,
    Account customer,
    Boolean isPerson
  ) {
    System.debug(
      LoggingLevel.INFO,
      LOG_PREFIX +
        ' Enrich customer id=' +
        (customer != null ? customer.Id : null) +
        ' isPerson=' +
        isPerson
    );
    if (Schema.SObjectType.Account.isUpdateable()) {
      if (isPerson) {
        customer.FirstName = String.isBlank(customer.FirstName)
          ? input.data.contractor.firstName
          : customer.FirstName;
        customer.LastName = String.isBlank(customer.LastName)
          ? input.data.contractor.lastName
          : customer.LastName;
        customer.PersonBirthdate = customer.PersonBirthdate == null
          ? input.data.contractor.birthDate
          : customer.PersonBirthdate;
      } else {
        customer.Name = String.isBlank(customer.Name)
          ? input.data.contractor.businessName
          : customer.Name;
      }
      customer.Email__c = String.isBlank(customer.Email__c)
        ? input.data.contractor.email
        : customer.Email__c;
      customer.Phone = String.isBlank(customer.Phone)
        ? input.data.contractor.phoneNumber
        : customer.Phone;

      try {
        update customer;
        System.debug(
          LoggingLevel.INFO,
          LOG_PREFIX +
            ' Customer updated id=' +
            (customer != null ? customer.Id : null)
        );
      } catch (Exception e) {
        System.debug(e);
        System.debug(
          LoggingLevel.WARN,
          LOG_PREFIX +
            ' Customer update failed id=' +
            (customer != null ? customer.Id : null) +
            ' reason=' +
            e.getMessage()
        );
      }
    }
  }

  private static Account getAgency(RequestWrapper input, RestResponse res) {
    /*   String targetAgency = 'AGE_' + input.data.salesNetwork.agency;
     List<Account> agencyList = [SELECT Id,Name FROM Account WHERE ExternalId__c =: targetAgency AND IsRolledOut__c = true AND RecordType.DeveloperName = 'Agency' LIMIT 1];
    
        if (agencyList.isEmpty()) {
            setResponse(res, 404, 'Agency not found');
            return null;
        }
    
        return agencyList.get(0);
     */
    String targetAgency = input.data.salesNetwork.agency;
    String targetAgencySociety = 'SOC_' + input.data.companyCode;
    System.debug('targetAgencySociety: ' + targetAgencySociety);
    System.debug(
      LoggingLevel.INFO,
      LOG_PREFIX +
        ' getAgency agency=' +
        targetAgency +
        ' company=' +
        (input != null && input.data != null ? input.data.companyCode : null) +
        ' targetAgencySociety=' +
        targetAgencySociety
    );
    List<FinServ__AccountAccountRelation__c> accountRelations = [
      SELECT
        FinServ__Account__r.Id,
        FinServ__Account__r.Name,
        FinServ__Account__r.AgencyCode__c
      FROM FinServ__AccountAccountRelation__c
      WHERE
        Identifier__c = :targetAgency
        AND RecordType.DeveloperName = 'AgencySociety'
        AND RelatedAccount_ExternalId__c = :targetAgencySociety
      LIMIT 1
    ];

    if (accountRelations.isEmpty()) {
      setResponse(res, 404, null, 'Agency not found');
      System.debug(
        LoggingLevel.WARN,
        LOG_PREFIX +
          ' Agency not found agency=' +
          targetAgency +
          ' company=' +
          (input != null && input.data != null ? input.data.companyCode : null)
      );
      return null;
    }
    Account agency = new Account(
      Id = accountRelations[0].FinServ__Account__r.Id,
      Name = accountRelations[0].FinServ__Account__r.Name
    );
    System.debug(
      LoggingLevel.INFO,
      LOG_PREFIX +
        ' Agency found id=' +
        agency.Id +
        ' code=' +
        accountRelations[0].FinServ__Account__r.AgencyCode__c
    );
    return agency;
  }

  private static OpportunityConfiguration__mdt getOpportunityConfig(
    RequestWrapper input,
    RestResponse res
  ) {
    if (Schema.sObjectType.OpportunityConfiguration__mdt.isAccessible()) {
      return [
        SELECT
          Id,
          DeveloperName,
          AutomaticMatching__c,
          AutomaticOpening__c,
          ManualOpening__c,
          AutomaticClosing__c,
          ManualClosing__c,
          AllowedCompanies__c,
          FlexCardId__c,
          MatchMergeFlowId__c,
          Merge__c,
          MatchableProducts__c,
          Split__c,
          ClosingType__c
        FROM OpportunityConfiguration__mdt
        WHERE DeveloperName = :input.data.channelCode
        LIMIT 1
      ];
    } else {
      setResponse(
        res,
        403,
        null,
        'Insufficient permissions to access OpportunityConfiguration__mdt object'
      );
      System.debug(
        LoggingLevel.WARN,
        LOG_PREFIX + ' No FLS to read OpportunityConfiguration__mdt'
      );
      return null;
    }
  }

  private static ProductConfiguration__mdt getProductConfig(
    RequestWrapper input,
    RestResponse res
  ) {
    if (Schema.sObjectType.ProductConfiguration__mdt.isAccessible()) {
      return [
        SELECT
          Id,
          DeveloperName,
          DomainType__c,
          QuoteComputation__c,
          ProductCode__c,
          Quote__c,
          Template__c
        FROM ProductConfiguration__mdt
        WHERE DomainType__c = :input.data.eventSource
        LIMIT 1
      ];
    } else {
      setResponse(
        res,
        403,
        null,
        'Insufficient permissions to access ProductConfiguration__mdt object'
      );
      System.debug(
        LoggingLevel.WARN,
        LOG_PREFIX + ' No FLS to read ProductConfiguration__mdt'
      );
      return null;
    }
  }

  private static void setResponse(
    RestResponse res,
    Integer statusCode,
    ResponseCorrelationWrapper bodyResponse,
    String message
  ) {
    ResponseWrapper responseBody = new ResponseWrapper();
    res.statusCode = statusCode;
    res.addHeader('Content-Type', 'application/json');
    responseBody.success = statusCode < 300;
    if (statusCode < 300) {
      responseBody.correlationId = bodyResponse;
      responseBody.tmstRegistration = System.now()
        .format('yyyy-MM-dd HH:mm:ss', 'Europe/Rome');
      responseBody.message = 'Operation completed successfully';
    } else {
      responseBody.message = message;
    }
    res.responseBody = Blob.valueOf(JSON.serialize(responseBody));
    System.debug(
      LoggingLevel.INFO,
      LOG_PREFIX +
        ' setResponse status=' +
        statusCode +
        ' success=' +
        (statusCode < 300)
    );
  }

  private static void logError(
    Exception exc,
    RestRequest request,
    RestResponse response
  ) {
    HttpRequest convertedRequest = convertRequest(request);
    HttpResponse convertedResponse = convertResponse(response);
    ErrLogMngClass logger = new ErrLogMngClass();
    logger.integrationErrLog(
      exc,
      convertedRequest,
      convertedResponse,
      OpportunityManagementWS.class.getName()
    );
  }

  private static HttpRequest convertRequest(RestRequest request) {
    HttpRequest result = new HttpRequest();
    result.setEndpoint(request.requestURI);
    result.setMethod(request.httpMethod);
    result.setBody(request.requestBody.toString());

    return result;
  }

  private static HttpResponse convertResponse(RestResponse response) {
    HttpResponse result = new HttpResponse();
    result.setBody(
      response.responseBody != null ? response.responseBody.toString() : null
    );
    result.setStatusCode(response.statusCode);

    return result;
  }

  private class RequestWrapper {
    public String id;
    public String source;
    public String type;
    public Datetime tmstRegistration;
    public RequestDataWrapper data;
  }

  private class RequestDataWrapper {
    public String objectId;
    public String objectType;
    public String eventId;
    public String eventType;
    public String type;
    public String eventOperation;
    public String eventSource;
    public Datetime eventIssueDate;
    public String companyCode;
    public String channelCode;
    public RequestSalesNetwork salesNetwork;
    public RequestBankInsurance bankInsurance;
    public RequestContract contract;
    public RequestContractor contractor;
    public RequestActivity activity;
    public OpportunityWSWrapper payload;
  }

  private class RequestSalesNetwork {
    public String agency;
    public String cip;
    public Boolean selectedByLocator;
  }

  private class RequestBankInsurance {
    public String bankId;
    public String branchId;
    public String agentId;
  }

  private class RequestContract {
    public String id;
    public String type;
    public String status;
    public String contractNumber;
    public String issuePortfolio;
    public String productCode;
    public Date validityStartDate;
    public Date validityEndDate;
    public Date effectiveStartDate;
    public Date effectiveEndDate;
    public Boolean flagOmniChannelSharing;
    public String intermediaryId;
  }

  private class RequestContractor {
    public String id;
    public String type;
    public String firstName;
    public String lastName;
    public String fiscalCode;
    public String vatNumber;
    public String businessName;
    public String privacyCode;
    public Date birthDate;
    public String email;
    public String phoneNumber;
  }

  private class ResponseWrapper {
    public ResponseCorrelationWrapper correlationId;
    public String tmstRegistration;
    public String message;
    public Boolean success;
  }

  private class ResponseCorrelationWrapper {
    public String quoteId;
    public String productId;
    public String opportunityId;
    public CaseCreationService.Result caseContext;
  }

  private class AsyncCalloutJob implements Queueable, Database.AllowsCallouts {
    private String integrationId;
    private String bodyJson;
    public AsyncCalloutJob(String integrationId, String bodyJson) {
      this.integrationId = integrationId;
      this.bodyJson = bodyJson;
    }
    public void execute(QueueableContext ctx) {
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX +
          ' AsyncCallout start integration=' +
          integrationId +
          ' bodySize=' +
          (bodyJson != null ? bodyJson.length() : 0)
      );
      IntegrationSettingUtility.executeHttpRequest(
        integrationId,
        bodyJson,
        null
      );
      System.debug(
        LoggingLevel.INFO,
        LOG_PREFIX + ' AsyncCallout done integration=' + integrationId
      );
    }
  }
}
