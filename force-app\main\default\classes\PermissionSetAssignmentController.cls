public with sharing class PermissionSetAssignmentController {
    @AuraEnabled
    public static void saveAssignments(List<PermissionSetAssignmentWrapper> assignments) {
        if (assignments == null || assignments.isEmpty()) {
            throw new AuraHandledException('La lista degli assignment è vuota.');
        }

        try {
            // Raccoglie tutti i PermissionSetId da processare
            Set<String> permissionSetIds = new Set<String>();
            for (PermissionSetAssignmentWrapper wrapper : assignments) {
                permissionSetIds.add(wrapper.PermissionSetId);
            }

            // Recupera gli assignment esistenti per l'utente
            Map<String, PermissionSetAssignment> existingAssignments = new Map<String, PermissionSetAssignment>();
            List<PermissionSetAssignment> existingRecords = [
                SELECT Id, PermissionSetId
                FROM PermissionSetAssignment
                WHERE AssigneeId = :assignments[0].AssigneeId
                AND PermissionSetId IN :permissionSetIds
            ];
            for (PermissionSetAssignment psa : existingRecords) {
                existingAssignments.put(psa.PermissionSetId, psa);
            }

            List<PermissionSetAssignment> toInsert = new List<PermissionSetAssignment>();
            List<PermissionSetAssignment> toDelete = new List<PermissionSetAssignment>();

            for (PermissionSetAssignmentWrapper wrapper : assignments) {
                Boolean isAssigned = existingAssignments.containsKey(wrapper.PermissionSetId);
                if (wrapper.Checked && !isAssigned) {
                    toInsert.add(new PermissionSetAssignment(
                        AssigneeId = wrapper.AssigneeId,
                        PermissionSetId = wrapper.PermissionSetId
                    ));
                } else if (!wrapper.Checked && isAssigned) {
                    toDelete.add(existingAssignments.get(wrapper.PermissionSetId));
                }
            }

            if (!toInsert.isEmpty()) {
                insert toInsert;
            }
            if (!toDelete.isEmpty()) {
                delete toDelete;
            }

        } catch (Exception e) {
            throw new AuraHandledException('Errore durante il salvataggio dei PermissionSetAssignment: ' + e.getMessage());
        }
    }

    public class PermissionSetAssignmentWrapper {
        @AuraEnabled public String assigneeId;
        @AuraEnabled public String permissionSetId;
        @AuraEnabled public Boolean checked;
    }
}
