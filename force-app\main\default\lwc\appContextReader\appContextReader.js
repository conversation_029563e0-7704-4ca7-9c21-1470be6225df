import { LightningElement, wire } from 'lwc';
import getCurrentAppLabel from '@salesforce/apex/AppContextController.getCurrentAppLabel';

export default class AppContextReader extends LightningElement {
    @wire(getCurrentAppLabel)
    wiredAppLabel({ error, data }) {
        if (data) {
            const event = new CustomEvent('appcontext', {
                detail: { appContext: data }
            });
            this.dispatchEvent(event);
        } else if (error) {
            console.error('Errore nel recupero del nome app:', error);
            const event = new CustomEvent('appcontext', {
                detail: { appContext: 'unknown' }
            });
            this.dispatchEvent(event);
        }
    }
}
