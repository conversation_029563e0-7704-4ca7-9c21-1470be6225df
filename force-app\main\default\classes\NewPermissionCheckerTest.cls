@isTest
public class NewPermissionCheckerTest {
    @isTest
    static void testIsNewCaseAllowedCreateable() {
        // Simula utente con permesso di creazione Case
        // In test, il permesso dipende dal profilo del running user
        Test.startTest();
        String jsonResp = NewPermissionChecker.isNewCaseAllowed();
        Test.stopTest();
        System.debug('Risposta JSON: ' + jsonResp);
        // Deserializza la risposta
        Map<String, Object> resp = (Map<String, Object>) JSON.deserializeUntyped(jsonResp);
        System.assert(resp.containsKey('isAllowed'), 'La risposta deve contenere isAllowed');
        Boolean isAllowed = (<PERSON>olean) resp.get('isAllowed');
        if (isAllowed) {
            System.assert(resp.containsKey('recordTypes'), 'Se isAllowed è true, deve restituire recordTypes');
            List<Object> rts = (List<Object>) resp.get('recordTypes');
            System.assert(rts.size() > 0, 'Deve restituire almeno un recordType disponibile');
        }
    }

    @isTest
    static void testGetCaseRTs() {
        Test.startTest();
        List<Map<String, String>> rts = NewPermissionChecker.getCaseRTs();
        Test.stopTest();
        System.assert(rts != null, 'La lista dei recordTypes non deve essere nulla');
        System.assert(rts.size() > 0, 'Deve restituire almeno un recordType disponibile');
        System.assert(rts[0].containsKey('Name'), 'Ogni recordType deve avere Name');
        System.assert(rts[0].containsKey('Id'), 'Ogni recordType deve avere Id');
    }

    @isTest
    static void testGetAvailableRecordTypeNamesForSObject() {
        Test.startTest();
        List<Map<String, String>> rts = NewPermissionChecker.getAvailableRecordTypeNamesForSObject(Case.SObjectType);
        Test.stopTest();
        System.assert(rts != null, 'La lista dei recordTypes non deve essere nulla');
        System.assert(rts.size() > 0, 'Deve restituire almeno un recordType disponibile');
        for (Map<String, String> rt : rts) {
            System.assert(rt.containsKey('Name'), 'Ogni recordType deve avere Name');
            System.assert(rt.containsKey('Id'), 'Ogni recordType deve avere Id');
        }
    }
}
