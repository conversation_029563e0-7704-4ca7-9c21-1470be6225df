MANUAL PROCEDURE: Fix Email Template Formula Fields Issue
========================================================

TICKET: urcs_CRM-XXX_FixTemplateEmailIssue
ENVIRONMENT: ALL
TYPE: POST-DEPLOYMENT
ESTIMATED TIME: 25 minutes

DESCRIPTION:
Fix both urcs_CaseNotificaCreazione and urcs_CaseNotificaChiusura email templates 
that are causing errors due to formula fields referencing null relationships.

ROOT CAUSE:
Both templates use formula fields TargaVeicoloFormula__c and ContractNameFormula__c 
that reference ContractAsset__r.Asset__r.Name and ContractAsset__r.ServiceContract__r.Name.
When these relationships are null, the email templates fail to render.

PREREQUISITES:
- System Administrator access
- Access to Email Templates

STEPS:

1. Fix urcs_CaseNotificaCreazione Template
   - Navigate to Setup > Email Templates
   - Search for "urcs_CaseNotificaCreazione"
   - Open the template and click "Source" to view HTML
   - Replace the current HTML with this safer version:

   <html style="overflow-y: hidden;">
   <head>
       <title></title>
   </head>
   <body style="height: auto; min-height: auto;">
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.Contact}}},</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">le confermiamo di aver ricevuto la richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>.</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
   <strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
   <strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
   <strong>Richiesta</strong>: {{{Case.Subject}}}</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Il nostro team sta analizzando la sua richiesta e la contatterà al più presto per fornirle maggiori dettagli o aggiornamenti.</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">La ringraziamo per aver scelto UnipolRental.<br />
   Buona giornata,<br />
   Servizio clienti UnipolRental</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa è una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
   </body>
   </html>

2. Fix urcs_CaseNotificaChiusura Template
   - Search for "urcs_CaseNotificaChiusura"
   - Open the template and click "Source" to view HTML
   - Replace the current HTML with this safer version:

   <html style="overflow-y: hidden;">
   <head>
       <title></title>
   </head>
   <body style="height: auto; min-height: auto;">
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.Contact}}},</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Le confermiamo che la sua richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>, è stata gestita e risolta con successo.</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
   <strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
   <strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
   <strong>Richiesta</strong>: {{{Case.Subject}}}</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">La ringraziamo per aver scelto UnipolRental.<br />
   Buona giornata,<br />
   Servizio clienti UnipolRental</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa è una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
   </body>
   </html>

3. Test Both Templates
   - Create a test case with a contact
   - Verify creation email sends successfully
   - Close the case and verify closure email sends successfully

4. OPTIONAL: Add Back Targa and Contratto Fields (if needed)
   - If business requires these fields, use alternative approach:
   - Add conditional logic or use safer field references
   - Consider using TargaVeicoloCanali__c instead of TargaVeicoloFormula__c

VALIDATION:
- Both templates send successfully without errors
- Email content displays correctly with all merge fields populated
- Flow execution completes without fault messages

ROLLBACK PLAN:
- Restore previous template HTML from backup
- Deactivate templates if issues persist

NOTES:
- Removed problematic formula fields: TargaVeicoloFormula__c and ContractNameFormula__c
- These fields reference complex relationships that may be null
- Templates now use only direct Case fields that are always available

COMPLETED BY: ________________
DATE: ________________
ENVIRONMENT: ________________
