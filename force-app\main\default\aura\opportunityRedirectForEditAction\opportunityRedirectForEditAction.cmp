<aura:component implements="lightning:isUrlAddressable,force:lightningQuickActionWithoutHeader,lightning:actionOverride,force:hasRecordId,force:hasSObjectName" access="global">
    
    
   
 <aura:attribute name="appContext" type="String" />
    <lightning:navigation aura:id="navService" />

    <c:appContextReader onappcontext="{!c.handleAppContext}" />

    <aura:handler name="init" value="{!this}" action="{!c.doRedirect}" />

    

</aura:component>


