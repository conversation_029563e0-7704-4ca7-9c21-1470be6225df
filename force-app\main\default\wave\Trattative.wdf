["{}", "{\"nodes\":{\"FORMULA6\":{\"sources\":[\"FORMULA5\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case when size(AreaOfNeed__c)<=1 then\\r\\n case \\r\\n\\twhen \\\"AreaOfNeedLinear\\\" in ('Casa', 'Famiglia', 'Cane e Gatto', 'Viaggio', 'Viaggi')\\r\\n\\tthen 'Unica - Casa e Famiglia'\\r\\n\\twhen \\\"AreaOfNeedLinear\\\" in ('Veicoli', 'Mobilità')\\r\\n\\tthen 'Unica - Veicoli e Mobilità'\\r\\n\\twhen \\\"AreaOfNeedLinear\\\" in ('Infortuni', 'Salute')\\r\\n\\tthen 'Unica - Persona'\\r\\n\\telse\\r\\n        'Nessuno'\\r\\nend\\r\\nelse\\r\\n\\tcase \\r\\n\\t\\twhen ( \\r\\n\\t\\tcontains(AreaOfNeedLinear,'Casa') or \\r\\n\\t\\tcontains(AreaOfNeedLinear,'Famiglia') or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Cane e Gatto') or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Viaggi')or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Veicoli')or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Mobilità')or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Infortuni')or\\r\\n\\t\\tcontains(AreaOfNeedLinear, 'Salute')\\r\\n\\t\\t)\\r\\n\\t\\tthen 'Unica - Multiambito'\\r\\n\\t\\telse\\r\\n\\t\\t'Multicomparto'\\r\\n\\t\\tend\\r\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"AmbitoFilter\",\"label\":\"Ambito\",\"type\":\"TEXT\"}]}},\"FORMULA14\":{\"sources\":[\"FORMULA11\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"'00e9X000007FpHWQA0'\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"SecurityOperatori\",\"label\":\"SecurityOperatori\",\"type\":\"TEXT\"}]}},\"FORMULA15\":{\"sources\":[\"FORMULA14\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"\\\"OppSharing.GroupSharing\\\"\",\"name\":\"GroupSharing\",\"label\":\"GroupSharing\",\"type\":\"MULTIVALUE\"}]}},\"FORMULA16\":{\"sources\":[\"FORMULA0\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case upper(Channel__c )\\r\\nwhen 'AGENZIA' then 'Altre'\\r\\nwhen 'CAMPAGNA' then 'Campagna' else Channel__c\\r\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"Origine\",\"label\":\"Origine\",\"type\":\"TEXT\"}]}},\"FORMULA17\":{\"sources\":[\"DROP_FIELDS2\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"\\\"DomainOpp.OppDomain\\\"\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"DomainExtract\",\"label\":\"DomainExtract\",\"type\":\"TEXT\"}]}},\"FORMULA18\":{\"sources\":[\"FORMULA15\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"\\\"OppSharing.UserSharing\\\"\",\"name\":\"UserSharing\",\"label\":\"UserSharing\",\"type\":\"MULTIVALUE\"}]}},\"FORMULA19\":{\"sources\":[\"FORMULA16\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case StageName\\nwhen 'Chiuso' then CloseDate\\nelse CreatedDate\\nend\",\"defaultValue\":\"\",\"name\":\"date_lavorate_formula\",\"format\":\"dd/MM/yyyy\",\"label\":\"Periodo formula \",\"type\":\"DATETIME\"}]}},\"DROP_FIELDS2\":{\"sources\":[\"FORMULA4\"],\"action\":\"schema\",\"parameters\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[\"DomainOpp.Id\"]},\"fields\":[]}},\"EDIT_ATTRIBUTES11\":{\"sources\":[\"EDIT_ATTRIBUTES10\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"Product__c\",\"label\":\"Prodotto\"},\"name\":\"Product__c\"}]}},\"DROP_FIELDS1\":{\"sources\":[\"FORMULA17\"],\"action\":\"schema\",\"parameters\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[\"Image.OppDom.Id\"]},\"fields\":[]}},\"EDIT_ATTRIBUTES12\":{\"sources\":[\"EDIT_ATTRIBUTES1\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"SalespointName__c\",\"label\":\"Punto Vendita\"},\"name\":\"SalespointName__c\"}]}},\"EDIT_ATTRIBUTES10\":{\"sources\":[\"EDIT_ATTRIBUTES9\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"ContactChannel__c\",\"label\":\"Canale di contatto\"},\"name\":\"ContactChannel__c\"}]}},\"FORMULA10\":{\"sources\":[\"FORMULA22\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"coalesce(Assignee__c,AssignedGroupName__c)\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"AssegnatarioName\",\"label\":\"Utente Assegnatario\",\"type\":\"TEXT\"}]}},\"FORMULA11\":{\"sources\":[\"JOIN6\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"'00e7Q000004Nir1QAC'\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"SecuritySysAdm\",\"label\":\"SecuritySysAdm\",\"type\":\"TEXT\"}]}},\"EDIT_ATTRIBUTES9\":{\"sources\":[\"EDIT_ATTRIBUTES8\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"AccountNameFormula__c\",\"label\":\"Soggetto\"},\"name\":\"AccountNameFormula__c\"}]}},\"EDIT_ATTRIBUTES8\":{\"sources\":[\"EDIT_ATTRIBUTES7\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"Name\",\"label\":\"Nome Trattativa\"},\"name\":\"Name\"}]}},\"OUTPUT0\":{\"sources\":[\"FORMULA18\"],\"action\":\"save\",\"parameters\":{\"measuresToCurrencies\":[],\"fields\":[],\"dataset\":{\"rowLevelSecurityFilter\":\"'GroupSharing' == \\\"$User.Id\\\" || 'UserSharing' == \\\"$User.Id\\\" || 'SecuritySysAdm' == \\\"$User.ProfileId\\\"\",\"rowLevelSharingSource\":\"Opportunity\",\"name\":\"Trattative_Dataset\",\"label\":\"Trattative Dataset\",\"folderName\":\"Developments\",\"type\":\"analyticsDataset\"}}},\"LOAD_DATASET3\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"Id\",\"Username\",\"LastName\",\"FirstName\",\"Name\",\"CompanyName\",\"Division\",\"Department\",\"Title\",\"Street\",\"City\",\"State\",\"PostalCode\",\"Country\",\"Email\",\"Phone\",\"MobilePhone\",\"Alias\",\"IsActive\",\"TimeZoneSidKey\",\"UserRoleId\",\"ReceivesInfoEmails\",\"UserType\",\"ForecastEnabled\",\"FullPhotoUrl\",\"SmallPhotoUrl\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"sourceObjectName\":\"User\",\"label\":\"User\",\"connectionName\":\"SFDC_LOCAL\",\"type\":\"connectedDataset\"}}},\"EDIT_ATTRIBUTES5\":{\"sources\":[\"EDIT_ATTRIBUTES4\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"Channel__c\",\"label\":\"Canale Origine\"},\"name\":\"Channel__c\"}]}},\"LOAD_DATASET1\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"Id\",\"MasterRecordId\",\"Name\",\"Type\",\"RecordTypeId\",\"ParentId\",\"BillingStreet\",\"BillingCity\",\"BillingState\",\"BillingPostalCode\",\"BillingCountry\",\"BillingLatitude\",\"BillingLongitude\",\"BillingGeocodeAccuracy\",\"ShippingStreet\",\"ShippingCity\",\"ShippingState\",\"ShippingPostalCode\",\"ShippingCountry\",\"ShippingLatitude\",\"ShippingLongitude\",\"ShippingGeocodeAccuracy\",\"Phone\",\"Fax\",\"AccountNumber\",\"Website\",\"PhotoUrl\",\"Sic\",\"Industry\",\"AnnualRevenue\",\"NumberOfEmployees\",\"Ownership\",\"TickerSymbol\",\"Description\",\"Rating\",\"Site\",\"OwnerId\",\"CreatedDate\",\"LastModifiedDate\",\"SystemModstamp\",\"LastActivityDate\",\"LastViewedDate\",\"LastReferencedDate\",\"IsPersonAccount\",\"Jigsaw\",\"JigsawCompanyId\",\"AccountSource\",\"SicDesc\",\"FinServ__AUM__c\",\"FinServ__ClientCategory__c\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"sourceObjectName\":\"Account\",\"label\":\"Account\",\"connectionName\":\"SFDC_LOCAL\",\"type\":\"connectedDataset\"}}},\"EDIT_ATTRIBUTES4\":{\"sources\":[\"EDIT_ATTRIBUTES3\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"Amount\",\"label\":\"Premio\"},\"name\":\"Amount\"}]}},\"LOAD_DATASET0\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"CloseDate\",\"IsClosed\",\"ClosureSubstatus__c\",\"Id\",\"IsDeleted\",\"AccountId\",\"IsPrivate\",\"Name\",\"Description\",\"StageName\",\"Amount\",\"Probability\",\"ExpectedRevenue\",\"TotalOpportunityQuantity\",\"Type\",\"NextStep\",\"LeadSource\",\"IsWon\",\"ForecastCategory\",\"ForecastCategoryName\",\"HasOpportunityLineItem\",\"Pricebook2Id\",\"OwnerId\",\"CreatedDate\",\"LastModifiedDate\",\"SystemModstamp\",\"LastActivityDate\",\"FiscalQuarter\",\"FiscalYear\",\"Fiscal\",\"LastViewedDate\",\"LastReferencedDate\",\"HasOpenActivity\",\"HasOverdueTask\",\"AssignedTo__c\",\"AreaOfNeed__c\",\"AreasOfNeedFormula__c\",\"AccountNameFormula__c\",\"AssignedGroupName__c\",\"Assignee__c\",\"Channel__c\",\"ContactChannel__c\",\"CreationDate__c\",\"HasCallMeBack__c\",\"HasMultipleAreasFormula__c\",\"JourneyStep__c\",\"OwnerNameFormula__c\",\"Product__c\",\"Rating__c\",\"WorkingSLAExpiryDate__c\",\"Salespoint__c\",\"HasCallMeBackFormula__c\",\"NameAndChannel__c\",\"SalespointName__c\",\"TemperatureFormula__c\",\"AssignedGroup__c\",\"AssignmentDestination__c\",\"ProductofInterest__c\",\"IsSetRef__c\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"sourceObjectName\":\"Opportunity\",\"label\":\"Opportunity\",\"connectionName\":\"SFDC_LOCAL\",\"type\":\"connectedDataset\"}}},\"EDIT_ATTRIBUTES7\":{\"sources\":[\"EDIT_ATTRIBUTES6\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"AssignedTo__c\",\"label\":\"Utente Assegnatario\"},\"name\":\"AssignedTo__c\"}]}},\"EDIT_ATTRIBUTES6\":{\"sources\":[\"EDIT_ATTRIBUTES5\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"JourneyStep__c\",\"label\":\"Journey Step\"},\"name\":\"JourneyStep__c\"}]}},\"EDIT_ATTRIBUTES1\":{\"sources\":[\"EDIT_ATTRIBUTES2\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"typeProperties\":{\"format\":\"dd-MM-yyyy\",\"type\":\"DATETIME\"},\"name\":\"WorkingSLAExpiryDate__c\",\"label\":\"Scadenza\"},\"name\":\"WorkingSLAExpiryDate__c\"}]}},\"EDIT_ATTRIBUTES0\":{\"sources\":[\"FORMULA10\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"typeProperties\":{\"format\":\"dd-MM-yyyy\",\"type\":\"DATETIME\"},\"name\":\"CloseDate\",\"label\":\"Data Chiusura\"},\"name\":\"CloseDate\"}]}},\"FORMULA0\":{\"sources\":[\"JOIN1\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"\\\"OpportunityOwner.Name\\\"\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"ReferenteFormula\",\"label\":\"Referente\",\"type\":\"TEXT\"}]}},\"EDIT_ATTRIBUTES3\":{\"sources\":[\"EDIT_ATTRIBUTES12\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"name\":\"StageName\",\"label\":\"Stato\"},\"name\":\"StageName\"}]}},\"EDIT_ATTRIBUTES2\":{\"sources\":[\"EDIT_ATTRIBUTES0\"],\"action\":\"schema\",\"parameters\":{\"fields\":[{\"newProperties\":{\"typeProperties\":{\"format\":\"dd-MM-yyyy\",\"type\":\"DATETIME\"},\"name\":\"CreatedDate\",\"label\":\"Data Creazione\"},\"name\":\"CreatedDate\"}]}},\"FORMULA2\":{\"sources\":[\"DROP_FIELDS1\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case \\r\\n\\twhen \\\"Rating__c\\\" = 'Tiepida'\\r\\n\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/temp3warmpng')\\r\\n\\r\\n\\twhen \\\"Rating__c\\\" = 'Calda'\\r\\n\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/temp2hotpng')\\t\\r\\n\\r\\n\\twhen \\\"Rating__c\\\" = 'Caldissima'\\r\\n\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/temp1veryhotpng')\\r\\n\\r\\n\\telse\\r\\n    concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/temp4coldpng')\\r\\n\\t\\r\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"RatingImage\",\"label\":\"Temperatura\",\"type\":\"TEXT\"}]}},\"FORMULA3\":{\"sources\":[\"FORMULA2\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case \\r\\n\\twhen \\\"HasCallMeBack__c\\\" = 'true'\\r\\n\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/Completed_Iconpng')\\t\\r\\n\\r\\n\\telse\\r\\n    concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/Expired_Iconpng')\\r\\n\\t\\r\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"CallMeBackImage\",\"label\":\"Call Me Back\",\"type\":\"TEXT\"}]}},\"LOAD_DATASET9\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"OppDomain\",\"Id\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"name\":\"DomainOpp\",\"label\":\"DomainOpp\",\"type\":\"analyticsDataset\"}}},\"FORMULA4\":{\"sources\":[\"JOIN7\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"array_join(AreaOfNeed__c, ',')\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"AreaOfNeedLinear\",\"label\":\"AreaOfNeedLinear\",\"type\":\"TEXT\"}]}},\"LOAD_DATASET8\":{\"sources\":[],\"action\":\"load\",\"parameters\":{\"fields\":[\"UserSharing\",\"GroupSharing\",\"Id\"],\"sampleDetails\":{\"sortBy\":[],\"type\":\"TopN\"},\"dataset\":{\"name\":\"OppWithUserAndGroupSharing\",\"label\":\"OppWithUserAndGroupSharing\",\"type\":\"analyticsDataset\"}}},\"FORMULA5\":{\"sources\":[\"FORMULA3\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case when size(AreaOfNeed__c)<=1 then\\r\\n\\tcase \\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Cane e Gatto'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/petpng')\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Casa'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/casapng')\\t\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Famiglia'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/famigliapng')\\t\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Infortuni'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/infortunipng')\\t\\r\\n\\t\\t\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Veicoli'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/veicolipng')\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Mobilità'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/mobilitapng')\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Salute'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/salutepng')\\t\\r\\n\\r\\n\\t\\twhen \\\"AreaOfNeedLinear\\\" = 'Viaggio'\\r\\n\\t\\tthen concat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/viaggipng')\\t\\r\\n\\r\\n\\t\\telse\\r\\n\\t\\tconcat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/emptypng')\\t\\r\\n\\t\\t\\r\\n\\tend\\r\\n\\t\\r\\nelse \\r\\n\\tcase when \\\"AreaOfNeedLinear\\\" in ('Casa', 'Famiglia', 'Cane e Gatto', 'Viaggio', 'Viaggi', 'Veicoli', 'Mobilità', 'Infortuni', 'Salute') \\r\\n\\tthen\\r\\n\\tconcat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/multicompartopng')\\t\\r\\n\\r\\n\\telse\\r\\n\\tconcat('https://',\\\"DomainExtract\\\",'file.force.com/file-asset/multiambitopng')\\r\\nend\\r\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"AmbitoImage\",\"label\":\"Ambito di protezione\",\"type\":\"TEXT\"}]}},\"FILTER0\":{\"sources\":[\"LOAD_DATASET0\"],\"action\":\"filter\",\"parameters\":{\"filterExpressions\":[{\"operands\":[\"PR\"],\"field\":\"Name\",\"type\":\"TEXT\",\"operator\":\"DOES_NOT_CONTAIN\"}]}},\"FORMULA20\":{\"sources\":[\"FORMULA19\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case ClosureSubstatus__c\\nwhen 'Polizza emessa' then 'Con vendita'\\nelse 'Senza vendita'\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"closure_status_formula\",\"label\":\"Tipologia\",\"type\":\"TEXT\"}]}},\"FORMULA21\":{\"sources\":[\"FORMULA20\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case \\n  when date_lavorate_formula >= CURRENT_DATE() - INTERVAL 90 DAY then 'Ultimi 3 mesi'\\n  when date_lavorate_formula >= CURRENT_DATE() - INTERVAL 180 DAY then 'Ultimi 6 mesi'\\n  when date_lavorate_formula >= CURRENT_DATE() - INTERVAL 270 DAY then 'Ultimi 9 mesi'\\n  when date_lavorate_formula >= CURRENT_DATE() - INTERVAL 365 DAY then 'Ultimi 12 mesi'\\n  else 'Oltre 12 mesi'\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"period_formula_field\",\"label\":\"Period Formula field\",\"type\":\"TEXT\"}]}},\"FORMULA22\":{\"sources\":[\"FORMULA21\"],\"action\":\"formula\",\"parameters\":{\"expressionType\":\"SQL\",\"fields\":[{\"formulaExpression\":\"case\\n  when CreatedDate >= CURRENT_DATE() - INTERVAL 90 DAY  then 'Ultimi 3 mesi'\\n  when CreatedDate >= CURRENT_DATE() - INTERVAL 180 DAY then 'Ultimi 6 mesi'\\n  when CreatedDate >= CURRENT_DATE() - INTERVAL 270 DAY then 'Ultimi 9 mesi'\\n  when CreatedDate >= CURRENT_DATE() - INTERVAL 365 DAY then 'Ultimi 12 mesi'\\n  else 'Oltre 12 mesi'\\nend\",\"defaultValue\":\"\",\"precision\":255,\"name\":\"CreateDate_Formula_period\",\"label\":\"CreateDate Formula period\",\"type\":\"TEXT\"}]}},\"JOIN1\":{\"schema\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[]},\"fields\":[]},\"sources\":[\"JOIN0\",\"LOAD_DATASET3\"],\"action\":\"join\",\"parameters\":{\"leftKeys\":[\"OwnerId\"],\"rightQualifier\":\"OpportunityOwner\",\"rightKeys\":[\"Id\"],\"joinType\":\"LEFT_OUTER\"}},\"JOIN0\":{\"schema\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[]},\"fields\":[]},\"sources\":[\"FILTER0\",\"LOAD_DATASET1\"],\"action\":\"join\",\"parameters\":{\"leftKeys\":[\"AccountId\"],\"rightQualifier\":\"AccountIDJoin\",\"rightKeys\":[\"Id\"],\"joinType\":\"LEFT_OUTER\"}},\"JOIN7\":{\"schema\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[]},\"fields\":[]},\"sources\":[\"EDIT_ATTRIBUTES11\",\"LOAD_DATASET9\"],\"action\":\"join\",\"parameters\":{\"leftKeys\":[\"Id\"],\"rightQualifier\":\"DomainOpp\",\"rightKeys\":[\"Id\"],\"joinType\":\"LOOKUP\"}},\"JOIN6\":{\"schema\":{\"slice\":{\"mode\":\"DROP\",\"ignoreMissingFields\":true,\"fields\":[]},\"fields\":[]},\"sources\":[\"FORMULA6\",\"LOAD_DATASET8\"],\"action\":\"join\",\"parameters\":{\"leftKeys\":[\"Id\"],\"rightQualifier\":\"OppSharing\",\"rightKeys\":[\"Id\"],\"joinType\":\"LOOKUP\"}}},\"ui\":{\"hiddenColumns\":[],\"connectors\":[{\"source\":\"FILTER0\",\"target\":\"JOIN0\"},{\"source\":\"LOAD_DATASET1\",\"target\":\"JOIN0\"},{\"source\":\"JOIN0\",\"target\":\"JOIN1\"},{\"source\":\"LOAD_DATASET3\",\"target\":\"JOIN1\"},{\"source\":\"JOIN1\",\"target\":\"TRANSFORM0\"},{\"source\":\"TRANSFORM7\",\"target\":\"OUTPUT0\"},{\"source\":\"TRANSFORM4\",\"target\":\"TRANSFORM3\"},{\"source\":\"JOIN7\",\"target\":\"TRANSFORM4\"},{\"source\":\"TRANSFORM6\",\"target\":\"TRANSFORM5\"},{\"source\":\"JOIN6\",\"target\":\"TRANSFORM7\"},{\"source\":\"TRANSFORM3\",\"target\":\"JOIN6\"},{\"source\":\"LOAD_DATASET0\",\"target\":\"FILTER0\"},{\"source\":\"LOAD_DATASET8\",\"target\":\"JOIN6\"},{\"source\":\"TRANSFORM0\",\"target\":\"TRANSFORM6\"},{\"source\":\"TRANSFORM5\",\"target\":\"JOIN7\"},{\"source\":\"LOAD_DATASET9\",\"target\":\"JOIN7\"}],\"nodes\":{\"FILTER0\":{\"top\":112,\"left\":252,\"description\":\"\",\"label\":\"Filter Product\",\"type\":\"FILTER\"},\"JOIN1\":{\"top\":112,\"left\":532,\"description\":\"\",\"label\":\"Opp vs User\",\"type\":\"JOIN\"},\"TRANSFORM4\":{\"connectors\":[{\"source\":\"DROP_FIELDS2\",\"target\":\"FORMULA17\"},{\"source\":\"FORMULA4\",\"target\":\"DROP_FIELDS2\"}],\"top\":112,\"left\":1232.2,\"description\":\"\",\"label\":\"Transform AmbitoDom\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA17\":{\"label\":\"DomainExtract\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"DROP_FIELDS2\":{\"label\":\"Drop Columns\"},\"FORMULA4\":{\"label\":\"AmbitoFormula\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"JOIN0\":{\"top\":112,\"left\":392,\"label\":\"Join 0\",\"type\":\"JOIN\"},\"TRANSFORM5\":{\"connectors\":[{\"source\":\"EDIT_ATTRIBUTES2\",\"target\":\"EDIT_ATTRIBUTES1\"},{\"source\":\"EDIT_ATTRIBUTES0\",\"target\":\"EDIT_ATTRIBUTES2\"},{\"source\":\"EDIT_ATTRIBUTES3\",\"target\":\"EDIT_ATTRIBUTES4\"},{\"source\":\"EDIT_ATTRIBUTES4\",\"target\":\"EDIT_ATTRIBUTES5\"},{\"source\":\"EDIT_ATTRIBUTES5\",\"target\":\"EDIT_ATTRIBUTES6\"},{\"source\":\"EDIT_ATTRIBUTES6\",\"target\":\"EDIT_ATTRIBUTES7\"},{\"source\":\"EDIT_ATTRIBUTES7\",\"target\":\"EDIT_ATTRIBUTES8\"},{\"source\":\"EDIT_ATTRIBUTES8\",\"target\":\"EDIT_ATTRIBUTES9\"},{\"source\":\"EDIT_ATTRIBUTES9\",\"target\":\"EDIT_ATTRIBUTES10\"},{\"source\":\"EDIT_ATTRIBUTES10\",\"target\":\"EDIT_ATTRIBUTES11\"},{\"source\":\"EDIT_ATTRIBUTES12\",\"target\":\"EDIT_ATTRIBUTES3\"},{\"source\":\"EDIT_ATTRIBUTES1\",\"target\":\"EDIT_ATTRIBUTES12\"}],\"top\":112.1,\"left\":952,\"description\":\"\",\"label\":\"Date Formatting\",\"type\":\"TRANSFORM\",\"graph\":{\"EDIT_ATTRIBUTES11\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES12\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES10\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES9\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES8\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES5\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES4\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES7\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES6\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES1\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES0\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES3\":{\"label\":\"Edit Attributes\"},\"EDIT_ATTRIBUTES2\":{\"label\":\"Edit Attributes\"}}},\"OUTPUT0\":{\"top\":112,\"left\":1792.2,\"description\":\"\",\"label\":\"Trattative_Dataset\",\"type\":\"OUTPUT\"},\"TRANSFORM6\":{\"connectors\":[],\"top\":112.1,\"left\":812,\"description\":\"\",\"label\":\"Transform Assegnatario Name\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA10\":{\"label\":\"AssegnatarioNome\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"LOAD_DATASET3\":{\"top\":252.2,\"left\":532,\"label\":\"User\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"TRANSFORM7\":{\"connectors\":[{\"source\":\"FORMULA11\",\"target\":\"FORMULA14\"},{\"source\":\"FORMULA14\",\"target\":\"FORMULA15\"},{\"source\":\"FORMULA15\",\"target\":\"FORMULA18\"}],\"top\":112,\"left\":1652.2,\"description\":\"\",\"label\":\"Transform Security\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA14\":{\"label\":\"SecurityOperatori\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA15\":{\"label\":\"GroupSharing\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA18\":{\"label\":\"UserSharing\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA11\":{\"label\":\"SecuritySysAdm\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"LOAD_DATASET1\":{\"top\":252,\"left\":392,\"label\":\"Account\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"LOAD_DATASET0\":{\"top\":112,\"left\":112,\"label\":\"Opportunity\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"JOIN7\":{\"top\":111.**************,\"left\":1092.2,\"description\":\"\",\"label\":\"Join Domain\",\"type\":\"JOIN\"},\"JOIN6\":{\"top\":112,\"left\":1512.2,\"description\":\"\",\"label\":\"Join SecurityOpp\",\"type\":\"JOIN\"},\"TRANSFORM0\":{\"connectors\":[{\"source\":\"FORMULA0\",\"target\":\"FORMULA16\"},{\"source\":\"FORMULA16\",\"target\":\"FORMULA19\"},{\"source\":\"FORMULA19\",\"target\":\"FORMULA20\"},{\"source\":\"FORMULA20\",\"target\":\"FORMULA21\"},{\"source\":\"FORMULA21\",\"target\":\"FORMULA22\"}],\"top\":112,\"left\":672,\"description\":\"Per aggiunta formule\",\"label\":\"Transform Fields\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA16\":{\"label\":\"Origine\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA19\":{\"label\":\"Lavorate Formula Date\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA0\":{\"label\":\"Referente\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA20\":{\"label\":\"Formula closure status\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA21\":{\"label\":\"Perido Formula String\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA22\":{\"label\":\"Formula period based createddate\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}},\"LOAD_DATASET9\":{\"top\":252,\"left\":1092,\"label\":\"DomainOpp\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"LOAD_DATASET8\":{\"top\":251.89999999999998,\"left\":1372,\"label\":\"OppWithUserAndGroupSharing\",\"type\":\"LOAD_DATASET\",\"parameters\":{\"sampleSize\":2000}},\"TRANSFORM3\":{\"connectors\":[{\"source\":\"DROP_FIELDS1\",\"target\":\"FORMULA2\"},{\"source\":\"FORMULA2\",\"target\":\"FORMULA3\"},{\"source\":\"FORMULA3\",\"target\":\"FORMULA5\"},{\"source\":\"FORMULA5\",\"target\":\"FORMULA6\"}],\"top\":112,\"left\":1372.2,\"description\":\"\",\"label\":\"Transform Image\",\"type\":\"TRANSFORM\",\"graph\":{\"FORMULA6\":{\"label\":\"AmbitoFilter\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"DROP_FIELDS1\":{\"label\":\"Drop OppImageID\"},\"FORMULA2\":{\"label\":\"RatingImage\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA3\":{\"label\":\"CallMeBackImage\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}},\"FORMULA5\":{\"label\":\"AmbitoImage\",\"parameters\":{\"type\":\"BASE_FORMULA_UI\"}}}}}},\"version\":\"64.0\",\"runMode\":\"full\"}"]