<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseCheckStatusChiusoAnnullato</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
    ISCHANGED(Status),
    ISPICKVAL(PRIORVALUE(Status),&quot;Chiuso - Annullato&quot;),
				OR(
        RecordType.DeveloperName == &quot;ur_CaseCRM&quot;,
        RecordType.DeveloperName == &quot;ur_CasePQ&quot;,
        RecordType.DeveloperName == &quot;ur_CaseSitoWeb&quot;,
        RecordType.DeveloperName == &quot;ur_CaseAR&quot;,
				  	 RecordType.DeveloperName == &quot;ur_CaseES&quot;
    ),
    OR(
        ISPICKVAL(Status, &quot;Nuova richiesta&quot;),
        ISPICKVAL(Status, &quot;In gestione&quot;),
        ISPICKVAL(Status, &quot;Trasferito&quot;),
        ISPICKVAL(Status, &quot;In attesa terze parti&quot;),
        ISPICKVAL(Status, &quot;Chiuso - Risolto&quot;),
        ISPICKVAL(Status, &quot;In attesa risposta cliente&quot;),
        ISPICKVAL(Status, &quot;Ricevuta risposta cliente&quot;),
        ISPICKVAL(Status, &quot;Mancata risposta cliente&quot;)
    )
)</errorConditionFormula>
    <errorMessage>Cambio di stato non accessibile</errorMessage>
</ValidationRule>
