public with sharing class QuoteEventTriggerHandler {

    //private static Boolean queueableLaunched = false; // <--- AGGIUNGI QUESTA RIGA

    /*public static void handle(List<Quote__e> events) {
        if (events == null || events.isEmpty()) return;

        // 1) Filtra solo eventi con CreaAnagrafica__c = false
        List<Quote__e> work = new List<Quote__e>();
        for (Quote__e ev : events) {
            if (ev.CreaAnagrafica__c == false) {
                work.add(ev);
            }
        }
        System.debug('QuoteEventTriggerHandler - work size ' + work.size());
        System.debug('QuoteEventTriggerHandler - work ' + work);

        if (work.isEmpty()) return;

        // 2) Recupera tutti i CF utili
        Set<String> fiscals = new Set<String>();
        for (Quote__e ev : work) {
            if (String.isNotBlank(ev.FiscalCode__c)) {
                fiscals.add(ev.FiscalCode__c);
            }
        }
        System.debug('QuoteEventTriggerHandler - fiscals ' + fiscals);

        Map<String, Account> allAccByCF = new Map<String, Account>();
        if(!fiscals.isEmpty()) {
            for (Account acc : [
                SELECT Id
                FROM Account
                WHERE FinServ__TaxId__pc IN :fiscals
            ]) {
                allAccByCF.put(acc.FinServ__TaxId__pc, acc);
            }
        }
        System.debug('QuoteEventTriggerHandler - allAccByCF ' + allAccByCF);

        // Controllo tra fiscals e allAccByCF: trova i CF non presenti nella mappa
        List<String> fiscalsNotInAccount = new List<String>();
        for (String cf : fiscals) {
            if (!allAccByCF.containsKey(cf)) {
                fiscalsNotInAccount.add(cf);
            }
        }
        System.debug('QuoteEventTriggerHandler - fiscalsNotInAccount ' + fiscalsNotInAccount);
            
        List<ShowPrice_Error__c> errors = new List<ShowPrice_Error__c>();

        if(!fiscalsNotInAccount.isEmpty()) {
            for (String missingCF : fiscalsNotInAccount) {
                for (Quote__e ev : work) {
                    if (String.isNotBlank(ev.FiscalCode__c) && ev.FiscalCode__c == missingCF) {
                        errors.add(buildError(ev, 'Account with FiscalCode__c ' + missingCF + ' not found.'));           
                    }
                }
            }
        }

        // 3) AccountDetails esistenti indicizzati per CF (query solo se serve)
        Map<String, AccountDetails__c> adByFiscal = new Map<String, AccountDetails__c>();
        if (!fiscals.isEmpty()) {
            for (AccountDetails__c ad : [
                SELECT Id, FiscalCode__c
                FROM AccountDetails__c
                WHERE FiscalCode__c IN :fiscals
            ]) {
                adByFiscal.put(ad.FiscalCode__c, ad);
            }
        }
        System.debug('QuoteEventTriggerHandler - adByFiscal ' + adByFiscal);*/

        /*Map<String, AccountAgencyRelation__c> aarByAccId = new Map<String, AccountAgencyRelation__c>();
        if (!fiscals.isEmpty()) {
            for (AccountAgencyRelation__c aar : [
                SELECT Id
                FROM AccountAgencyRelation__c
                WHERE ExternalId__c LIKE '%' + :fiscals + '%'

            ]) {
                aarByAccId.put(aar.FinServ__Account__c, aar);
            }
        }*/

        // 4) RecordType "Individual" (se esiste)
       /* Id adIndividualRT = null;
        try {
            adIndividualRT = Schema.SObjectType.AccountDetails__c
                .getRecordTypeInfosByDeveloperName().get('Individual').getRecordTypeId();
        } catch (Exception ignore) {
            System.debug('⚠️ RecordType Individual non trovato, continuo senza.');
        }

        // String societyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountSociety' LIMIT 1].Id;
        // System.debug('##societyAarRTId##: ' + societyAarRTId);
        // String agencyAarRTId = [SELECT Id FROM RecordType WHERE SObjectType = 'FinServ__AccountAccountRelation__c' AND DeveloperName = 'AccountAgency' LIMIT 1].Id;
        // System.debug('##agencyAarRTId##: ' + agencyAarRTId);

        // Liste bulk per inserimenti
        List<AccountDetails__c> adToInsert = new List<AccountDetails__c>();
        Map<String, Quote__e> evByCF = new Map<String, Quote__e>();

        // Id agencyId = [SELECT FinServ__Account__c FROM FinServ__AccountAccountRelation__c WHERE RecordTypeId =: agencySocietyAarRTId AND Identifier__c =: agencyCodeRaw LIMIT 1].FinServ__Account__c;
                
        List<FinServ__AccountAccountRelation__c> listAarToInsert = new List<FinServ__AccountAccountRelation__c>();


        // 5) Crea AccountDetails mancanti
        for (Quote__e ev : work) {
            String cf = ev.FiscalCode__c;
            if (String.isBlank(cf)) {
                errors.add(buildError(ev, 'Missing FiscalCode__c on Platform Event.'));
                continue;
            }
            if (!adByFiscal.containsKey(cf)) {


                // FinServ__AccountAccountRelation__c aarCompany = new FinServ__AccountAccountRelation__c();
                // aarCompany.FinServ__RelatedAccount__c = companyId;
                // aarCompany.FinServ__Account__c = accId;
                // aarCompany.FinServ__ExternalId__c = anagrafica.codiceFiscale + '_SOC_' + companyCodeRaw;
                // aarCompany.RecordTypeId = societyAarRTId;
        
                // for(FinServ__ReciprocalRole__c rr : reciprocalRoles){
                //     if('Cliente'.equalsIgnoreCase(rr.Name) && 'Compagnia'.equalsIgnoreCase(rr.FinServ__InverseRole__c)){
                //         aarCompany.FinServ__Role__c = rr.Id;
                //     }
                // }

                // listAarToInsert.add(aarCompany);

                // AccountDetails__c ad = new AccountDetails__c(FiscalCode__c = cf);
                // if (adIndividualRT != null) ad.RecordTypeId = adIndividualRT;
                // ad.Relation__c = aarCompany.Id;
                // adToInsert.add(ad);
                // evByCF.put(cf, ev);
            }
        }
        if(!listAarToInsert.isEmpty()){
            Database.SaveResult[] resAAR = Database.insert(listAarToInsert, false);
        }
        System.debug('QuoteEventTriggerHandler - adToInsert ' + adToInsert);
        System.debug('QuoteEventTriggerHandler - evByCF ' + evByCF);

        if (!adToInsert.isEmpty()) {
            Database.SaveResult[] resAD = Database.insert(adToInsert, false);
            for (Integer i = 0; i < resAD.size(); i++) {
                String cf = adToInsert[i].FiscalCode__c;
                if (resAD[i].isSuccess()) {
                    adByFiscal.put(cf, new AccountDetails__c(Id = resAD[i].getId(), FiscalCode__c = cf));
                } else {
                    errors.add(buildError(evByCF.get(cf), 'AccountDetails__c insert failed: ' + firstErr(resAD[i])));
                }
            }
        }

        // 6) Quote da creare (ora idempotenti)
        List<Quote> quotesToUpsert = new List<Quote>();
        Map<Integer, Quote__e> evByIndex = new Map<Integer, Quote__e>();
        HashUtil hasher = new HashUtil();

        for (Integer i = 0; i < work.size(); i++) {
            Quote__e ev = work[i];
            String cf = ev.FiscalCode__c;

            if (String.isBlank(cf) || !adByFiscal.containsKey(cf)) continue;

            Quote q = new Quote();
            q.Name                = 'Quote for ' + cf;
            q.CommercialStatus__c = ev.CommercialStatus__c;
            q.AreasOfNeed__c      = ev.AreasOfNeed__c;
            q.QuoteAmount__c      = ev.QuoteAmount__c;
            q.Status              = 'Show price';
            q.EngagementPoint__c  = ev.EngagementPoint__c;
            q.CIP__c              = ev.CIP__c;
            q.To_Work__c          = true;
            q.ExpirationDate      = ev.ExpirationDate__c;
            if (ev.CreatedDate != null) {
                q.CreatedDateTPD__c = ev.CreatedDate.date();
            }

            // === Idempotenza Quote: chiave esterna (ExternalId__c) ===
            String evTs = (ev.CreatedDate == null)
                ? ''
                : ev.CreatedDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'');
            List<String> quoteParts = new List<String>();
            quoteParts.add(String.valueOf(cf));
            quoteParts.add(String.valueOf(ev.CIP__c));
            quoteParts.add(String.valueOf(ev.QuoteAmount__c));
            quoteParts.add(evTs);
            String quoteKeyRaw = String.join(quoteParts, '|');
            q.ExternalId__c = hasher.sha256(quoteKeyRaw);

            quotesToUpsert.add(q);
            evByIndex.put(quotesToUpsert.size() - 1, ev); // indice → evento
        }

        // Upsert Quote per ExternalId__c
        List<Quote> upsertedQuotes = new List<Quote>();
        if (!quotesToUpsert.isEmpty()) {
            Database.UpsertResult[] resQ = Database.upsert(
                quotesToUpsert,
                Quote.Fields.ExternalId__c,
                false
            );
            for (Integer i = 0; i < resQ.size(); i++) {
                if (resQ[i].isSuccess()) {
                    upsertedQuotes.add(new Quote(Id = resQ[i].getId()));
                } else {
                    errors.add(buildError(evByIndex.get(i), 'Quote upsert failed: ' + firstErr(resQ[i])));
                    upsertedQuotes.add(null);
                }
            }
        }

        // 7) Coverage (1 per evento) — idempotenti
        List<OpportunityCoverage__c> covToUpsert = new List<OpportunityCoverage__c>();
        Map<Integer, Quote__e> evByCov = new Map<Integer, Quote__e>();

        for (Integer i = 0; i < upsertedQuotes.size(); i++) {
            Quote qRef = upsertedQuotes[i];
            if (qRef == null) continue;

            Quote__e ev = evByIndex.get(i);

            OpportunityCoverage__c cov = new OpportunityCoverage__c(
                Quote__c           = qRef.Id,
                Asset__c           = ev.Asset__c,
                Description__c     = ev.Description__c,
                Amount__c          = ev.Amount__c,
                EngagementPoint__c = ev.EngagementPoint__c,
                Fractionation__c   = ev.Fractionation__c,
                Conventions__c     = ev.Conventions__c
            );

            // === Idempotenza Coverage: chiave esterna (ExternalId__c) ===
            String covTs = (ev.CreatedDate == null)
                ? ''
                : ev.CreatedDate.formatGmt('yyyy-MM-dd\'T\'HH:mm:ss\'Z\'');
            List<String> covParts = new List<String>();
            covParts.add(String.valueOf(qRef.Id));
            covParts.add(covTs);
            covParts.add(String.valueOf(ev.Asset__c));
            covParts.add(String.valueOf(ev.Amount__c));
            covParts.add(String.valueOf(ev.Fractionation__c));
            covParts.add(String.valueOf(ev.Conventions__c));
            String covKeyRaw = String.join(covParts, '|');
            cov.ExternalId__c = hasher.sha256(covKeyRaw);

            covToUpsert.add(cov);
            evByCov.put(covToUpsert.size() - 1, ev);
        }

        if (!covToUpsert.isEmpty()) {
            Database.UpsertResult[] resC = Database.upsert(
                covToUpsert,
                OpportunityCoverage__c.Fields.ExternalId__c,
                false
            );
            for (Integer i = 0; i < resC.size(); i++) {
                if (!resC[i].isSuccess()) {
                    errors.add(buildError(evByCov.get(i), 'OpportunityCoverage__c upsert failed: ' + firstErr(resC[i])));
                }
            }
        }

        // 8) Salva eventuali errori in un colpo solo
        if (!errors.isEmpty()) {
            insert errors;
        }

        // 9) Avvia orchestrator (retry + batch) da decommentare MS
        // if (!queueableLaunched) { // <--- AGGIUNGI QUESTO CONTROLLO
        //     System.enqueueJob(new ShowPriceRetryAndBatchQueueable());
        //     queueableLaunched = true; // <--- AGGIUNGI QUESTA RIGA
        // }
    }

    // ——— Helpers ———
    private static ShowPrice_Error__c buildError(Quote__e ev, String message) {
        return new ShowPrice_Error__c(
            Status__c       = 'Failed',
            RetryCount__c   = 0,
            ErrorMessage__c = message,
            Payload__c      = ev != null ? JSON.serialize(ev) : null
        );
    }

    private static String firstErr(Database.SaveResult sr) {
        if (sr == null || sr.getErrors().isEmpty()) return 'Unknown';
        Database.Error err = sr.getErrors()[0];
        return err.getStatusCode() + ' - ' + err.getMessage();
    }
    private static String firstErr(Database.UpsertResult ur) {
        if (ur == null || ur.getErrors().isEmpty()) return 'Unknown';
        Database.Error err = ur.getErrors()[0];
        return err.getStatusCode() + ' - ' + err.getMessage();
    }

    // === Utility hash (inner non-statica) ===
    private class HashUtil {
        public String sha256(String s) {
            return EncodingUtil.convertToHex(
                Crypto.generateDigest('SHA-256', Blob.valueOf(s == null ? '' : s))
            );
        }*/
    }