------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to remove the "Intelligence View" buttons and the "Individua Società" button from the List Views of Account, Contact, and Lead objects in Salesforce.
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Go to Setup, Home
3. Go to Object Manager and enter the "Account" Object
4. Click on "List View Button Layout"
5. Click on the arrow icon and click on "Edit" link
6. Locate the checkbox for "Intelligence View" and uncheck it
7. Locate the checkbox for "Individua Società" and uncheck it
8. Click the save button on the bottom of the page
9. Repeat steps 3 to 8 for "Contact" and "Lead" objects

-------------------------------------------------------------------------------
------ REMOVE "Individua Società" BUTTON FROM ACCOUNT LIST VIEWS --------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Go to Setup, Home
3. Go to Object Manager and enter the "Account" Object
4. Click on "List View Button Layout"
5. Click on the arrow icon and click on "Edit" link
6. Locate the checkbox for "Individua Società" and uncheck it
7. Click the save button on the bottom of the page

-------------------------------------------------------------------------------
------ REMOVE "Storico Caricamenti" BUTTON FROM CASE LIST VIEWS ---------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Go to Setup, Home
3. Go to Object Manager and enter the "Case" Object
4. Click on "List View Button Layout"
5. Click on the arrow icon and click on "Edit" link
6. Locate the checkbox for "Storico Caricamenti" and uncheck it
7. Click the save button on the bottom of the page

-------------------------------------------------------------------------------
------ VERIFY AND REMOVE UNWANTED CASE LIST VIEWS -----------------------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Go to the "Cases" tab from the App Launcher
3. In the List View dropdown (top left), review all available Case List Views
4. Ensure that only the following List Views are present:
    - Attività in gestione
    - Attività Operative recentemente visualizzate
    - Attività Personali
    - Attività Scadute
    - tutte le attvità
    - recenti
5. For any other List View:
    5.1. Select the unwanted List View
    5.2. Click the gear icon (⚙️) next to the List View name
    5.3. Click "Delete"
    5.4. Confirm deletion
6. Repeat step 5 for all unwanted List Views
7. Verify that only the specified List Views remain
