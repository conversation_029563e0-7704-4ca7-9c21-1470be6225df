global without sharing class MenuStrumentiOmnistudio implements System.Callable {
    
    private static final String GENERIC_ERROR = 'Si è verificato un errore';

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName.equals('getPreferiti')) {
                getPreferiti(inputs, output);
            } else if(methodName.equals('getLDAPStrumentiHome')) {
                getLDAPStrumentiHome(inputs, output);
            } else if(methodName.equals('getModaleRinnovi')) {
                getModaleRinnovi(inputs, output);
            } else if(methodName.equals('removePreferito')) {
                removePreferito(inputs, output);
            } else {
                output.put('success', false);
                output.put('errorMessage', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }

        } catch (Exception e) {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
            result = false;
        }

        System.debug('///output: ' + output);

        return result;
    }

    private void getPreferiti(Map<String, Object> inputs, Map<String, Object> output) {

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        UserUtils.UserContext userContext = UserUtils.getUserContext();
        UserUtils.UserNetwork userPreferred = userContext.societyPrefered;
        
        /*Set<String> staticApiName = new  Set<String>{
            'UN_CAMPAGNE',
            'UN_AGENZIA_CTRL_FASTDATA',
            'US_AGENZIA_CTRL_FASTDATA'
        };*/

        List<Asset> assetList = [
            SELECT User__c,
                Category__c,
                Value__c,
                Key__c,
                Fei_Parameters__c,
                Fei_Link__c,
                Status
            FROM   Asset
            WHERE  User__c = :UserInfo.getUserId()
            AND    Category__c = 'Preferiti'
            AND    RecordTypeId = :assetRtId
            AND    Status = 'Registered'
        ];
        System.debug('assetList:: ' + assetList);

        if (assetList == null || assetList.isEmpty()) {
            output.put('success', true);
            output.put('result', new List<Map<String, Object>>());
            return;
        }

        // raccogliamo i developerName dai preferiti su Asset
        Set<String> apiNameString = new Set<String>();
        Map<String, Asset> assetByKey = new Map<String, Asset>();
        for (Asset ast : assetList) {
            /*if(staticApiName.contains(ast.Key__c)) {
                continue;
            }*/

            apiNameString.add(ast.Key__c);
            assetByKey.put(ast.Key__c, ast);
        }
        System.debug('apiNameString:: ' + apiNameString);
        System.debug('assetByKey:: ' + assetByKey);

        // prendo i metadati esistenti
        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                DeveloperName,
                Is_Active__c,
                Parent__c,
                Context__c,
                Section__c,
                Order__c,
                Type__c,
                FEI_ID__c,
                Redirect_Link__c,
                Redirect_Type__c,
                Profile_Code__c,
                Group_Codes__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  DeveloperName IN :apiNameString
        ];
        System.debug('rows:: ' + rows);

        // Map di metadata per accesso rapido
        Map<String, Menu_Strumenti_Tree_Structure__mdt> metaByDev = new Map<String, Menu_Strumenti_Tree_Structure__mdt>();
        for (Menu_Strumenti_Tree_Structure__mdt row : rows) {
            metaByDev.put(row.DeveloperName, row);
        }
        System.debug('rows:: ' + metaByDev);

        // 4) Costruisco FEI uniformi
        List<Map<String, Object>> result = new List<Map<String, Object>>();
        for (String devName : apiNameString) {
            if (metaByDev.containsKey(devName)) {
                // esiste nel metadata costruisco da metadata
                Menu_Strumenti_Tree_Structure__mdt m = metaByDev.get(devName);
                System.debug('Tree Structure:: ' + m);
                
                Map<String, Object> obj = new Map<String, Object>();
                obj.put('masterLabel', m.MasterLabel);
                obj.put('developerName', m.DeveloperName);
                obj.put('feiId', m.FEI_ID__c);
                obj.put('type', m.Type__c);
                obj.put('redirectLink', m.Redirect_Link__c);
                obj.put('redirectType', m.Redirect_Type__c);

                if(assetByKey.get(devName).Fei_Parameters__c != null) {
                    Map<String,Object> paramObj = (Map<String,Object>) safeParams(assetByKey.get(devName).Fei_Parameters__c);
                    obj.put('params', safeParams(String.valueOf(paramObj.get('param'))));
                }

                Boolean isProfileCodeEnable = m.Profile_Code__c != null ? userContext.profileName.containsIgnoreCase(m.Profile_Code__c) : true;
                Boolean isGroupEnabled = m.Group_Codes__c != null ? false : true;
                
                if(m.Group_Codes__c != null && userPreferred != null) {
                    isGroupEnabled = userPreferred.checkAnyGroup(m.Group_Codes__c.split(';'));
                } 

                obj.put('enabled', isProfileCodeEnable && isGroupEnabled);

                System.debug('obj:: ' + obj);
                result.add(obj);
            } else {
                // NON esiste nel metadata costruisco dal record Asset
                Asset ast = assetByKey.get(devName);
                System.debug('ast:: ' + ast);
                Map<String, Object> obj = new Map<String, Object>();
                obj.put('masterLabel', ast.Value__c);
                obj.put('developerName', ast.Key__c);
                //obj.put('redirectLink', ast.Fei_Link__c);
                //obj.put('params', safeParams(ast.Fei_Parameters__c));
                obj.put('type', 'FEI');
                obj.put('enabled', true);

                if(ast.Fei_Parameters__c != null) {
                    Map<String,Object> paramObj = (Map<String,Object>) safeParams(ast.Fei_Parameters__c);

                    if(paramObj.get('type') != null) {
                        if(paramObj.get('type') == 'GET') {

                            obj.put('feiId', 'FEI.LINK.GET');
                            obj.put('redirectLink', paramObj.get('link'));
                            obj.put('params', safeParams(String.valueOf(paramObj.get('param'))));
                        } else if(paramObj.get('type') == 'POST') {

                            obj.put('feiId', 'FEI.LINK.POST');

                            Map<String, Object> mapRequest = new Map<String, Object>();
                            mapRequest.put('properties', safeParams(String.valueOf(paramObj.get('param'))));
                            mapRequest.put('requestUrl', paramObj.get('link'));
                            mapRequest.put('requestMethod', paramObj.get('type'));

                            obj.put('params', mapRequest);
                        }
                    } else {
                        obj.put('redirectLink', paramObj.get('link'));
                        obj.put('params', safeParams(String.valueOf(paramObj.get('param'))));
                    }
                }

                System.debug('obj:: ' + obj);
                result.add(obj);
            }

        }

        output.put('success', true);
        output.put('result', result);
    }

    private void getLDAPStrumentiHome(Map<String, Object> inputs, Map<String, Object> output) {

        UserUtils.UserContext usrContext = UserUtils.getUserContext();

        Map<String, Object> result = new Map<String, Object>();

        Set<String> rinnoviApiName = new  Set<String>{
            'UN_CAMPAGNE',
            'UN_AGENZIA_CTRL_FASTDATA'
        };

        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                DeveloperName,
                Is_Active__c,
                Group_Codes__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  DeveloperName IN :rinnoviApiName
        ];

        UserUtils.UserNetwork userPreferred = usrContext.societyPrefered;

        for(Menu_Strumenti_Tree_Structure__mdt r : rows) {
            if(r.Group_Codes__c != null && userPreferred != null) {
                result.put(r.DeveloperName, userPreferred.checkAnyGroup(r.Group_Codes__c.split(';')));
            } else if(r.Group_Codes__c == null) {
                result.put(r.DeveloperName, true);
            } else {
                result.put(r.DeveloperName, false);
            }
        }

        output.put('success', true);
        output.put('result', result);
    }

    private void getModaleRinnovi(Map<String, Object> inputs, Map<String, Object> output) {

        UserUtils.UserContext usrContext = UserUtils.getUserContext();

        Map<String, Object> result = new Map<String, Object>();

        Set<String> rinnoviApiName = new  Set<String>{
            'US_RINNOVI_AZIONI_PIANIFICAZIONEQT',
            'US_RINNOVI_AZIONI_STAMPADOC',
            'US_RINNOVI_AVVSCAD_ARRETRATI',
            'US_RINNOVI_AVVSCAD_CONFBASI',
            'US_RINNOVI_AVVSCAD_GESTAVV',
            'US_RINNOVI_MONITOR_GESTIONALE',
            'US_RINNOVI_MONITOR_OPERATIVO'
        };

        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                DeveloperName,
                Is_Active__c,
                Group_Codes__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  DeveloperName IN :rinnoviApiName
        ];

       UserUtils.UserNetwork userPreferred = usrContext.societyPrefered;

        for(Menu_Strumenti_Tree_Structure__mdt r : rows) {
            if(r.Group_Codes__c != null && userPreferred != null) {
                result.put(r.DeveloperName, userPreferred.checkAnyGroup(r.Group_Codes__c.split(';')));
            } else {
                result.put(r.DeveloperName, false);
            }
        }

        result.put('fiscalCode', usrContext.user.FederationIdentifier);
        result.put('society', usrContext.societyPreferedCode);

        output.put('success', true);
        output.put('result', result);
    }   

    private void removePreferito(Map<String, Object> inputs, Map<String, Object> output) {
        
        String developerName = inputs.containsKey('developerName') ? String.valueOf(inputs.get('developerName')) : null;

        if(developerName == null) {
            output.put('success', false);
            return;
        }

        //Map<String, Object> result = new Map<String, Object>();

        System.debug(developerName);
        MenuStrumentiController.removeUserFavorite(developerName);

        output.put('success', true);
    }

    private static Object safeParams(String raw) {
        if (String.isBlank(raw)) return null;

        try {
            return JSON.deserializeUntyped(raw); // torna Map<String,Object> o List<Object>
        } catch (Exception e) {
            return raw; // fallback: lo lasci come stringa
        }
    }
}