<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;SV_CalloutBody&quot; : null,
    &quot;DMT_GetAgencyInfo&quot; : null,
    &quot;DM_GetAccountAgencyDetails&quot; : null,
    &quot;DME_GetInsurancePolicyKey&quot; : null,
    &quot;DME_GetCaseInfo&quot; : null,
    &quot;SV_ResponseBasedOnConditions&quot; : null,
    &quot;ResponseAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AccountGetInfoNew</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>b5be26b7-20b5-4abd-9bb2-51faa33b5eb2</globalKey>
        <inputFieldName>AgencyCode__c</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AccountGetInfoNew</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>acc</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>ID</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>79a91d47-f1fe-455c-96cb-83e625c1f1c9</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AccountGetInfoNew</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>acc</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X00000sfj5iQAA&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Turbo Extract</type>
    <uniqueName>AccountGetInfoNew_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
