import { LightningElement, wire, track, api } from 'lwc';
import { EnclosingTabId, getTabInfo, openSubtab } from 'lightning/platformWorkspaceApi'; // Importing methods for working with tabs

import edit_icon from '@salesforce/resourceUrl/EditIcon'; // Importing the resource URL for edit icon

const EDIT_ICON = edit_icon + '/edit.png'; // Combining the resource URL with the edit icon path

export default class AccordionTabletHeader extends LightningElement {
  @api opportunityRecordId; // Public property to hold the opportunity record ID
  @track subject = 'Modifica preventivo esistente'; // Trackable property for the subject of the opportunity
  
  @api quote; // Public property to hold the quote information
  @api source; // Parent opportunity source channel
  @track showPreview = false;
  @track showFEI = false;
  flowName = "FEIQuickActionUNICA";
  flowTitle = "Modifica Preventivo";
  @track flowTitle = "Modifica Preventivo";
  @track flowTitleModifica = "Modifica Preventivo";
  @track flowTitleVisualizza = "Visualizza Preventivo";

  @track flowInputVariables = [];

  previewTitle = 'Anteprima Preventivo';

  @track feiTypeModify = 'MODIFICA_PREVENTIVO';
  @track feiTypeView = 'CONSULTAZIONE_CONTRATTO';
  @track feiTypeValueParam = 'CONSULTAZIONE_CONTRATTO';

  get isPrevidenza()
  {
    return this.source == 'Preventivatore Previdenza';
  }

  // Getter to determine if the opportunity is closed
  get isOpportunityClosed() { 
    return this.quote.isOpportunityClosed; 
  }

  // Getter to determine if this is the first quote
  get displayHeaderTitle() { 
    return this.quote.isFirst; 
  }

  // Getter to retrieve the name of the quote
  get name() { 
    return this.quote.name; 
  }

  // Getter to retrieve the record ID of the quote
  get recordId() {
    return this.quote.recordId; 
  }

  // Getter to retrieve the areas of need images
  get areasOfNeedImages() { 
    return this.quote.areasOfNeedImages; 
  }

  // Getter to retrieve the total amount of the quote
  get totalAmount() { 
    return this.quote.totalAmount; 
  }

  // Getter to retrieve the status of the quote
  get status() { 
    return this.quote.status; 
  }

  // Getter to retrieve the creation date of the quote
  get creationDate() { 
    return this.quote.creationDate; 
  }

  // Getter to retrieve the expiration date of the quote
  get expirationDate() { 
    return this.quote.expirationDate; 
  }

  // Getter to retrieve the unique link of the quote
  get unicaLink() { 
    return this.quote.unicaLink; 
  }

  // Getter to retrieve the opportunity coverages of the quote
  get opportunityCoverages() { 
    return this.quote.opportunityCoverages; 
  }

  get monthlyContribution() { 
    return this.quote.monthlyContribution; 
  }

  // Getter to determine the label for showing/hiding opportunity coverages
  get showOpportunityCoverages() { 
    return this.showContent ? 'Nascondi ambiti' : 'Vedi ambiti'; 
  }

  // Getter to retrieve the URL of the edit icon
  get edit_icon() { 
    return EDIT_ICON; 
  }

  @wire (EnclosingTabId) tabId; // Wiring to get the ID of the enclosing tab

  // Method to handle click on the quote name
  async handleNameClick() {
    if (!this.tabId) {
      return;
    }

    const tabInfo = await getTabInfo(this.tabId); // Get information about the enclosing tab
    const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId; // Determine the primary tab ID

    await openSubtab(primaryTabId, { recordId: this.recordId, focus: true }); // Open a subtab with the quote record ID
  }

  // Method to handle click on "Modifica" button
  // handleModificaClick() {
  //   this.startFlow();
  // }

   // Method to handle a click on the "Modifica" button
   handleModificaClick() {
    this.isFeiTypeModify=true; 
    this.startFlow(); // Uncomment to start the flow
  }

// Method to handle a click on the "Visualizza" button
  handleVisualizzaClick() {
      this.isFeiTypeModify=false; 
      this.startFlow(); // Uncomment to start the flow
  }
  handleStatusChange(event)
  {
    if (event.detail.status === 'FINISHED') {
        this.showFEI = false;
    }
  }

  // Method to start the flow
  startFlow() {
        if(this.isFeiTypeModify){
          this.flowInputVariables =
              [{
                  name: 'recordId',
                  type: 'String',
                  value: this.quote.recordId
              },{
                  name: 'feiType',
                  type: 'String',
                  value: this.feiTypeModify
              },{
                  name:  'society',
                  type:  'String',
                  value: 'SOC_1'
              }];
          this.flowTitle=this.flowTitleModifica;
        }else{
            this.flowInputVariables =
                [{
                    name: 'recordId',
                    type: 'String',
                    value: this.quote.recordId
                },{
                    name: 'feiType',
                    type: 'String',
                    value: this.feiTypeView
                },{
                    name:  'society',
                    type:  'String',
                    value: 'SOC_1'
                }];
                this.flowTitle=this.flowTitleVisualizza;
        }
        console.log(JSON.stringify(this.flowInputVariables));
        this.showFEI = true;
  }

  handleClose()
  {
    this.showFEI = false;
  }

  @track showContent = false; // Trackable property to control the visibility of opportunity coverages
  @track buttonIcon = 'utility:chevronright'; // Trackable property to control the icon for toggling visibility

  // Method to toggle the visibility of opportunity coverages
  toggleContent() {
    this.showContent = !this.showContent; // Toggle the showContent property
    this.buttonIcon = this.showContent ? 'utility:chevrondown' : 'utility:chevronright'; // Set the button icon based on visibility
  }
  openPDFPreview()
  {
    this.showPreview = true;
  }

  closePDFPreview()
  {
    this.showPreview = false;
  }
}