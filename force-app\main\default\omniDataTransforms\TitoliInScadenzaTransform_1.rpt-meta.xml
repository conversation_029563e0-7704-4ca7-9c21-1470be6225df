<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>[ {
  &quot;uplCrmQuietId&quot; : &quot;&quot;,
  &quot;idContrattoPTF&quot; : &quot;7359394b-c6e5-4779-872a-e819f9533350&quot;,
  &quot;xquietanzaId&quot; : &quot;213621734&quot;,
  &quot;contId&quot; : &quot;922339426762273302&quot;,
  &quot;isFEAAttiva&quot; : false,
  &quot;isFEAVisualizzata&quot; : true,
  &quot;isProdottoUnico&quot; : true,
  &quot;isFirmaAttiva&quot; : true,
  &quot;isRettificaAttiva&quot; : true,
  &quot;isIncassaAttiva&quot; : true,
  &quot;isCompletaAttiva&quot; : false,
  &quot;Unibox&quot; : &quot;&quot;,
  &quot;Firmatario&quot; : &quot;&quot;,
  &quot;Rettificabile&quot; : &quot;&quot;,
  &quot;Titoli al legale&quot; : &quot;&quot;,
  &quot;Modello&quot; : &quot;&quot;,
  &quot;Targa&quot; : &quot;&quot;,
  &quot;Esposizione AR&quot; : false,
  &quot;Omnicanalità&quot; : &quot;&quot;,
  &quot;Frazionamento&quot; : &quot;MENSILE&quot;,
  &quot;N. Folder&quot; : &quot;101853000212733&quot;,
  &quot;Premio&quot; : 0,
  &quot;Scadenza&quot; : &quot;2025-05-20T22:00:00&quot;,
  &quot;Numero Polizza/Posizione&quot; : [ 192135424 ],
  &quot;Ambito&quot; : &quot;&quot;,
  &quot;Tipo&quot; : &quot;4&quot;,
  &quot;Società&quot; : &quot;1&quot;
} ]</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TitoliInScadenzaTransform</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom1452</globalKey>
        <inputFieldName>prodottoAbilitatoInvioFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>prodottoAbilitatoInvioFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>d6fc5baa-8748-4182-a461-f00dac8f8f18</globalKey>
        <inputFieldName>polizzaPosizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>polizzaPosizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom870</globalKey>
        <inputFieldName>dataEffettoPolizza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>dataEffettoPolizza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>b0db4b23-6b43-45d8-b78a-8ae2710211f5</globalKey>
        <inputFieldName>ramo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ramo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>f1144044-0f03-4ed8-9509-51e77ec28519</globalKey>
        <inputFieldName>isSostituzioneAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isSostituzioneAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:ramo &quot;/&quot; | var:NumeroPolizza|1 TOSTRING CONCAT</formulaConverted>
        <formulaExpression>CONCAT(ramo,&quot;/&quot;,TOSTRING(NumeroPolizza|1))</formulaExpression>
        <formulaResultPath>polizzaPosizione</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>TitoliInScadenzaTransformCustom2468</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3594</globalKey>
        <inputFieldName>tipoOp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipoOp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3697</globalKey>
        <inputFieldName>inviiAr</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>inviiAr</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom5251</globalKey>
        <inputFieldName>ITCTValue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ITCTValue</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom5623</globalKey>
        <inputFieldName>agenziaMadre</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agenziaMadre</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom4623</globalKey>
        <inputFieldName>idTitolo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idTitolo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom679</globalKey>
        <inputFieldName>isVariazioneAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isVariazioneAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom1410</globalKey>
        <inputFieldName>subagenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>subAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>0b122537-8307-4fb6-84cd-f86068a65419</globalKey>
        <inputFieldName>isInvioDaRemotoAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isInvioDaRemotoAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom1393</globalKey>
        <inputFieldName>emessaNonIncassabile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>emessaNonIncassabile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem14</globalKey>
        <inputFieldName>idContrattoPTF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idContrattoPTF</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem15</globalKey>
        <inputFieldName>Società</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol&quot;,
  &quot;4&quot; : &quot;UniSalute&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem12</globalKey>
        <inputFieldName>uplCrmQuietId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>uplCrmQuietId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem13</globalKey>
        <inputFieldName>xquietanzaId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>xquietanzaId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem2</globalKey>
        <inputFieldName>Modello</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>modello</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Omnicanalità &quot;D&quot; = &quot;Multicanalità/\/\/Dispositiva&quot; | var:Omnicanalità &quot;I&quot; = &quot;Multicanalità/\/\/informativa&quot; &quot;Multicanalità/\/\/off&quot; IF &quot;Multicanalità/\/\/off&quot; IF</formulaConverted>
        <formulaExpression>IF(Omnicanalità = &quot;D&quot;, &quot;Multicanalità Dispositiva&quot;,IF(Omnicanalità = &quot;I&quot;,  &quot;Multicanalità informativa&quot;, &quot;Multicanalità off&quot;), &quot;Multicanalità off&quot;)</formulaExpression>
        <formulaResultPath>omnicanalita</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>TitoliInScadenzaTransformCustom8740</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>94d4102e-2ee8-4401-827f-4ae4bdd720a7</globalKey>
        <inputFieldName>isVediDocumentoAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isVediDocumentoAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem3</globalKey>
        <inputFieldName>NumeroFolder</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>nFolder</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem11</globalKey>
        <inputFieldName>Premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem0</globalKey>
        <inputFieldName>firmata</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>firmata</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem20</globalKey>
        <inputFieldName>omnicanalita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>omnicanalita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem1</globalKey>
        <inputFieldName>Frazionamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>frazionamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom1115</globalKey>
        <inputFieldName>inviiRemoti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>inviiRemoti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom6643</globalKey>
        <inputFieldName>dataEffettoTitolo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>dataEffettoTitolo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem18</globalKey>
        <inputFieldName>Titoli al legale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>titoliAlLegale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem10</globalKey>
        <inputFieldName>isFirmaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFirmaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom8655</globalKey>
        <inputFieldName>idDoc</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idDoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem16</globalKey>
        <inputFieldName>Targa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>targa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem8</globalKey>
        <inputFieldName>isFEAVisualizzata</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFEAVisualizzata</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem17</globalKey>
        <inputFieldName>Tipo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;0&quot; : &quot;Copertura Provvisoria&quot;,
  &quot;1&quot; : &quot;Nuova Emissione&quot;,
  &quot;2&quot; : &quot;Sostituzione&quot;,
  &quot;3&quot; : &quot;Variazione&quot;,
  &quot;4&quot; : &quot;Quietanza&quot;,
  &quot;5&quot; : &quot;Premio Unico&quot;,
  &quot;6&quot; : &quot;Regolazione Premio&quot;,
  &quot;7&quot; : &quot;Carta Verde&quot;,
  &quot;8&quot; : &quot;Penale&quot;,
  &quot;9&quot; : &quot;Rimborso Premio&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem9</globalKey>
        <inputFieldName>isProdottoUnico</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isProdottoUnico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem23</globalKey>
        <inputFieldName>isCompletaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isCompletaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem6</globalKey>
        <inputFieldName>contId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>contId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom4490</globalKey>
        <inputFieldName>agenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem7</globalKey>
        <inputFieldName>isFEAAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFEAAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem24</globalKey>
        <inputFieldName>Ambito</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ambito</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;RCA&quot; : &quot;Auto&quot;,
  &quot;RE&quot; : &quot;Danni non auto&quot;,
  &quot;REL&quot; : &quot;Danni non auto&quot;,
  &quot;VITA&quot; : &quot;Vita&quot;,
  &quot;VIT&quot; : &quot;Vita&quot;,
  &quot;Altri&quot; : &quot;n.d.&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem4</globalKey>
        <inputFieldName>Unibox</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unibox</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem21</globalKey>
        <inputFieldName>Rettificabile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>rettificabile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem5</globalKey>
        <inputFieldName>EsposizioneAR</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>esposizioneAR</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem22</globalKey>
        <inputFieldName>Scadenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(dd-MM-yyyy)</outputFieldFormat>
        <outputFieldName>dataScadenzaTitolo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom7312</globalKey>
        <inputFieldName>numeroAppendice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>numeroAppendice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom4165</globalKey>
        <inputFieldName>numeroArchivio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>numeroArchivio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom8186</globalKey>
        <inputFieldName>docItemId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>docItemId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem25</globalKey>
        <inputFieldName>isIncassaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isIncassaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3122</globalKey>
        <inputFieldName>numeroArchivioProdottoUnico</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>numeroArchivioProdottoUnico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9V000000rsgbUAAItem26</globalKey>
        <inputFieldName>isRettificaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isRettificaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3441</globalKey>
        <inputFieldName>tipoDocumento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipoDocumento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3765</globalKey>
        <inputFieldName>NumeroPolizza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>polizza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3665</globalKey>
        <inputFieldName>operazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>operazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom2502</globalKey>
        <inputFieldName>dtEffVar</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>dtEffVar</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>21d9c7d5-842c-41cc-b588-b477867e739a</globalKey>
        <inputFieldName>isStampaEssigAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isStampaEssigAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom2498</globalKey>
        <inputFieldName>isStampabileInFEA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isStampabileInFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom5628</globalKey>
        <inputFieldName>Firmatario</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>firmatario</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom3571</globalKey>
        <inputFieldName>isIncassoAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isIncassoAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom5862</globalKey>
        <inputFieldName>NPOLFLDValue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>NPOLFLDValue</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>[ {
  &quot;tipoDocumento&quot; : &quot;Q&quot;,
  &quot;idDoc&quot; : &quot;&quot;,
  &quot;dataEffettoTitolo&quot; : &quot;2024-10-26&quot;,
  &quot;numeroAppendice&quot; : &quot;0&quot;,
  &quot;ramo&quot; : &quot;77&quot;,
  &quot;uplCrmQuietId&quot; : &quot;184521142&quot;,
  &quot;idContrattoPTF&quot; : &quot;b7a4d568-d614-40e0-9e4b-4ac43fa19eff&quot;,
  &quot;xquietanzaId&quot; : &quot;203274538&quot;,
  &quot;contId&quot; : &quot;639271535797114902&quot;,
  &quot;isFEAAttiva&quot; : false,
  &quot;isFEAVisualizzata&quot; : false,
  &quot;isProdottoUnico&quot; : true,
  &quot;isFirmaAttiva&quot; : true,
  &quot;isRettificaAttiva&quot; : true,
  &quot;isIncassaAttiva&quot; : true,
  &quot;isCompletaAttiva&quot; : false,
  &quot;Unibox&quot; : null,
  &quot;Firmatario&quot; : &quot;&quot;,
  &quot;Rettificabile&quot; : &quot;&quot;,
  &quot;Titoli al legale&quot; : &quot;&quot;,
  &quot;Modello&quot; : null,
  &quot;Targa&quot; : null,
  &quot;Esposizione AR&quot; : false,
  &quot;Omnicanalità&quot; : &quot;&quot;,
  &quot;Frazionamento&quot; : &quot;MENSILE&quot;,
  &quot;N. Folder&quot; : &quot;101853000120295&quot;,
  &quot;Premio&quot; : 0,
  &quot;Scadenza&quot; : &quot;2024-10-25T22:00:00&quot;,
  &quot;Numero Polizza/Posizione&quot; : [ 192058290, 192058291 ],
  &quot;Ambito&quot; : &quot;REL&quot;,
  &quot;Tipo&quot; : &quot;3&quot;,
  &quot;Società&quot; : &quot;1&quot;
} ]</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TitoliInScadenzaTransform_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
