import { LightningElement,api,track } from 'lwc';

export default class ProfilazioneDetailComponent extends LightningElement {

 @api records = [];
 @track localRecords = [];
 
 connectedCallback(){
     
    let jsonString = JSON.stringify(this.records);
    console.log(jsonString);
    let parsedObject = JSON.parse(jsonString);
 
    if(parsedObject[0].mappingDataPS != null){

      if(parsedObject[0].isUserDataNull == true){
          this.localRecords = parsedObject[0].mappingDataPS;
      }
      else{
          this.mergePermissions(parsedObject[0]);
      }
     }
     else{
      this.localRecords = parsedObject;
     }
    

 }


  mergePermissions(json) {
      try{
            console.log('###TEST Merging Permissions...');
            console.log('##DEBUG input:', JSON.stringify(json));
            const mappingDataPS = json.mappingDataPS;
            const permissionsMappingName = json.PermissionsMappingName;
            console.log('##DEBUG permissionsMappingName:', JSON.stringify(permissionsMappingName));

            // 1. Crea un set di PermissionSet da PermissionsMappingName
            console.log('##DEBUG before  permissionsMappingName.map 1');
            const permissionSetToTrue = new Set(
            permissionsMappingName.map(item => item.PermissionSet)
            );
            console.log('##DEBUG after permissionsMappingName.map 1');

            // 2. Crea una mappa per accedere rapidamente ai permessi completi da PermissionsMappingName
            const permissionDetailsMap = new Map(
            permissionsMappingName.map(item => [item.PermissionSet, item])
            );
            console.log('##DEBUG after permissionsMappingName.map 2 ');

            // 3. Crea un oggetto intermedio per evitare duplicati
            const updatedMap = new Map();
            console.log('##DEBUG after step 3');

            // 4. Prima: aggiorna tutti quelli esistenti in mappingDataPS
            mappingDataPS.forEach(item => {
            const isInSelected = permissionSetToTrue.has(item.PermissionSet);
            updatedMap.set(item.PermissionSet, {
               ...item,
               Checked: isInSelected ? true : item.Checked
            });
            });
            console.log('##DEBUG after step 4');
            // 5. Poi: aggiungi quelli che esistono solo in PermissionsMappingName
            permissionSetToTrue.forEach(permissionSet => {
            if (!updatedMap.has(permissionSet)) {
               updatedMap.set(permissionSet, {
                  ...permissionDetailsMap.get(permissionSet),
                  Checked: true
               });
            }
            });
            console.log('##DEBUG after step 5');

            // 6. Risultato finale
            this.localRecords = Array.from(updatedMap.values());
         } catch (error) {
               console.error('##TEST Error merging permissions:', error);
               console.error('##DEBUG error:', error, error.stack);
         }
      }





 handleChange(event){
    console.log('##TESDT in handlechange event.target.dataset: '+event.target.dataset);
    const id = event.target.dataset.id;
    const value = event.target.checked;
    console.log('##TESDT in handlechange id: '+id);
    console.log('##TESDT in handlechange value: '+value);
 }
}