public with sharing class Quote<PERSON>riggerHandler {

    public static void onAfterInsertNotification(List<Quote> newList) {
        List<NotificationResponseWrapper> listResult = new List<NotificationResponseWrapper>();
        Set<Id> oppIdSet = new Set<Id>();
        for (Quote q : newList) {
            if (!String.isBlank(q.Opportunity.Parent__c)) {
                oppIdSet.add(q.Opportunity.Parent__c);
            } else {
                oppIdSet.add(q.OpportunityId);
            }
        }

        List<Opportunity> oppList = [SELECT Id, Name, AssignedTo__c, AssignedGroup__c FROM Opportunity WHERE Id IN :oppIdSet];
        
        Set<Id> groupIds = new Set<Id>();
        for (Opportunity opp : oppList) {
            if (opp.AssignedGroup__c != null) {
                groupIds.add(opp.AssignedGroup__c);
            }
        }
        List<GroupMember> groupMembers = new List<GroupMember>();
        if (!groupIds.isEmpty()) {
            groupMembers = [
                SELECT GroupId, UserOrGroup.Id
                FROM GroupMember
                WHERE GroupId IN :groupIds AND UserOrGroup.IsActive = true
            ];
        }

        for (Opportunity opp : oppList) {
            Set<Id> currentGroupIds = new Set<Id>();
            if (opp.AssignedGroup__c != null && groupMembers.size() > 0) {
                for (GroupMember gm : groupMembers) {
                    if(opp.AssignedGroup__c == gm.GroupId){
                        currentGroupIds.add(gm.UserOrGroup.Id);
                    }
                }
            }
            for (Quote q : newList) {
                NotificationResponseWrapper wrap = new NotificationResponseWrapper(
                    opp.Id,
                    opp.AssignedTo__c != null ? opp.AssignedTo__c : null,
                    currentGroupIds != null && !currentGroupIds.isEmpty() ? currentGroupIds : null,
                    q.Name,
                    opp.Name
                );
                listResult.add(wrap);
            }
        }

        if(listResult.size () > 0){
           NotificationHandler.quoteNotification(listResult, 'Creation');
        }
    }

    public class NotificationResponseWrapper {
        public String id { get; set; }
        public String assignedTo { get; set; }
        public Set<Id> assignedGroupTo { get; set; }
        public String quoteName { get; set; }
        public String opportunityName { get; set; }

        public NotificationResponseWrapper(String id, String assignedTo, Set<Id> assignedGroupTo, String quoteName, String opportunityName){
            this.id = id;
            this.assignedTo = assignedTo;
            this.assignedGroupTo = assignedGroupTo;
            this.quoteName = quoteName;
            this.opportunityName = opportunityName;
        }
    }
}