<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Custom_Error_Message_1</name>
        <label>Show error msg no assegnatario</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Per effettuare l&apos;operazione è necessario prendere in carico il case.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Errore_from_address_no_reply</name>
        <label>Errore from address no reply</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Se selezionato il flag &quot;In attesa risposta del cliente&quot; scegliere un indirizzo mittente diverso dal No Reply.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Errore_generico</name>
        <label>Errore generico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Si è verificato un errore: impossibile completare l&apos;operazione.
 
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Show_error_msg_stato</name>
        <label>Show error msg stato</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Non è possibile inviare email in stati diversi da &quot;In gestione&quot;.</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Check_flag_in_attesa_risposta_cliente</name>
        <label>Check flag in attesa risposta cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InAttesaRispCliente__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_no_reply_email</targetReference>
            </connector>
            <label>SI</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_from_address_selected</name>
        <label>Check from address selected</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_no_reply</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.FromAddress</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Get_no_reply_email.Address</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Errore_from_address_no_reply</targetReference>
            </connector>
            <label>is no reply</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckcaseInfo</name>
        <label>Check  case Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>no_assegnatario</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.UtAssegnatario__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>currentUserId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Custom_Error_Message_1</targetReference>
            </connector>
            <label>no assegnatario</label>
        </rules>
        <rules>
            <name>Non_in_gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Show_error_msg_stato</targetReference>
            </connector>
            <label>Non in gestione</label>
        </rules>
        <rules>
            <name>assegnatario_in_gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_flag_in_attesa_risposta_cliente</targetReference>
            </connector>
            <label>assegnatario &amp;&amp; in gestione</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>currentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <formulas>
        <name>EmailMsgCreateddatePlus7Days</name>
        <dataType>DateTime</dataType>
        <expression>{!$Record.CreatedDate} +7</expression>
    </formulas>
    <interviewLabel>urcs_RTEmailMessageBeforeIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTEmailMessageBeforeIU</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_no_reply_email</name>
        <label>Get no-reply email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_from_address_selected</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unipol Rental CS No Reply</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsVerified</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckcaseInfo</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
        <filterFormula>AND(
    {!$Record.ParentId} != NULL,
    OR(
      {!$Record.Parent.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
      {!$Record.Parent.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CasePQ&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseSitoWeb&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseES&apos;
     ),
    {!$Record.Incoming} == false
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>urRTidList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
