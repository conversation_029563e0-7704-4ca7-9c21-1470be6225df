@isTest
public class OpportunityEditOverrideControllerTest {

    // Metodo di utilità per recuperare un'opportunità con RecordType specifico
    private static Opportunity creaOpportunity(String recordTypeDevName, Boolean chiusa, Boolean vinta) {
        Id rtId = getRecordTypeId(recordTypeDevName);
        // Se il RecordTypeId non è valido, provo a prendere il primo attivo
        if (rtId == null) {
            List<RecordType> attivi = [SELECT Id FROM RecordType WHERE SObjectType = 'Opportunity' AND IsActive = true LIMIT 1];
            rtId = attivi.isEmpty() ? null : attivi[0].Id;
        }
        // Se ancora null, salto la creazione e loggo
        if (rtId == null) {
            System.debug('Nessun RecordType valido trovato per Opportunity. Test saltato.');
            return null;
        }
        String stage;
        if (chiusa) {
            stage = vinta ? 'Closed Won' : 'Closed Lost';
        } else {
            stage = 'Prospecting';
        }
        Opportunity opp = new Opportunity(
            Name = 'Test Opp ' + recordTypeDevName,
            StageName = stage,
            CloseDate = Date.today().addDays(10),
            RecordTypeId = rtId
        );
        insert opp;
        return opp;
    }

    // Recupera l'Id del RecordType tramite DeveloperName
    private static Id getRecordTypeId(String devName) {
        List<RecordType> rts = [SELECT Id FROM RecordType WHERE SObjectType = 'Opportunity' AND DeveloperName = :devName LIMIT 1];
        return rts.isEmpty() ? null : rts[0].Id;
    }

    @isTest
    static void testEditAllowedProdotto() {
        Test.startTest();
        Opportunity opp = creaOpportunity('Prodotto', false, false);
        if (opp == null) {
            System.debug('Test saltato: RecordType Prodotto non disponibile.');
            return;
        }
        OpportunityEditOverrideController.EditResponse resp = OpportunityEditOverrideController.checkEditAccess(opp.Id);
        Test.stopTest();
        System.assert(resp.isEditAllowed, 'La modifica dovrebbe essere consentita per Prodotto');
        System.assertEquals('', resp.message, 'Nessun messaggio di errore atteso');
    }

    @isTest
    static void testEditAllowedOmnicanale() {
        Test.startTest();
        Opportunity opp = creaOpportunity('Omnicanale', false, false);
        if (opp == null) {
            System.debug('Test saltato: RecordType Omnicanale non disponibile.');
            return;
        }
        OpportunityEditOverrideController.EditResponse resp = OpportunityEditOverrideController.checkEditAccess(opp.Id);
        Test.stopTest();
        System.assert(resp.isEditAllowed, 'La modifica dovrebbe essere consentita per Omnicanale');
        System.assertEquals('', resp.message, 'Nessun messaggio di errore atteso');
    }

    @isTest
    static void testEditNotAllowedOtherRecordType() {
        Test.startTest();
        Opportunity opp = creaOpportunity('AltroTipo', false, false);
        if (opp == null) {
            System.debug('Test saltato: RecordType AltroTipo non disponibile.');
            return;
        }
        OpportunityEditOverrideController.EditResponse resp = OpportunityEditOverrideController.checkEditAccess(opp.Id);
        Test.stopTest();
        System.assert(!resp.isEditAllowed, 'La modifica non dovrebbe essere consentita per altri tipi');
        System.assert(resp.message.contains('La modifica è consentita solo'), 'Messaggio di errore atteso');
    }

    @isTest
    static void testEditNotAllowedClosedOpportunity() {
        Test.startTest();
        Opportunity opp = creaOpportunity('Prodotto', true, true);
        if (opp == null) {
            System.debug('Test saltato: RecordType Prodotto non disponibile.');
            return;
        }
        OpportunityEditOverrideController.EditResponse resp = OpportunityEditOverrideController.checkEditAccess(opp.Id);
        Test.stopTest();
        System.assert(!resp.isEditAllowed, 'La modifica non dovrebbe essere consentita per opportunità chiusa');
        System.assert(resp.message.contains('opportunità chiusa'), 'Messaggio di errore atteso');
    }

    @isTest
    static void testNoAccessOrException() {
        Test.startTest();
        // Passo un Id non valido per simulare eccezione
        OpportunityEditOverrideController.EditResponse resp = OpportunityEditOverrideController.checkEditAccess(null);
        Test.stopTest();
        System.assert(!resp.isEditAllowed, 'La modifica non dovrebbe essere consentita senza Id valido');
        System.assert(resp.message.contains('errore'), 'Messaggio di errore atteso');
    }
}
