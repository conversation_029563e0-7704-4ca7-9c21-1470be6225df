<?xml version="1.0" encoding="UTF-8"?>
<Profile xmlns="http://soap.sforce.com/2006/04/metadata">
    <applicationVisibilities>
        <application>urcs_CustomerServiceApp</application>
        <default>true</default>
        <visible>true</visible>
    </applicationVisibilities>
    <custom>true</custom>
    <layoutAssignments>
        <layout>Case-urcs_CaseLayout</layout>
        <recordType>Case.ur_CaseES</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>Case-urcs_CaseLayout</layout>
        <recordType>Case.ur_CaseAR</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>Case-urcs_CaseLayout</layout>
        <recordType>Case.ur_CaseCRM</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>Case-urcs_CaseLayout</layout>
        <recordType>Case.ur_CasePQ</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>Case-urcs_CaseLayout</layout>
        <recordType>Case.ur_CaseSitoWeb</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>EmailMessage-urcs_EmailMessageLayout</layout>
    </layoutAssignments>
    <layoutAssignments>
        <layout>Task-urcs_TaskOperatore</layout>
        <recordType>Task.ur_TaskOperatore</recordType>
    </layoutAssignments>
    <layoutAssignments>
        <layout>CaseMilestone-urcs_CaseMilestoneLayout</layout>
    </layoutAssignments>
    <layoutAssignments>
        <layout>urcs_ConfigCaseSla__mdt-urcs_ConfigCaseSla Layout</layout>
    </layoutAssignments>
    <layoutAssignments>
        <layout>urcs_ConfigUfficiCanali__mdt-urcs_ConfigUfficiCanali Layout</layout>
    </layoutAssignments>
    <layoutAssignments>
        <layout>urcs_ConfigUfficiSuggeriti__mdt-urcs_ConfigUfficiSuggeriti Layout</layout>
    </layoutAssignments>
    <recordTypeVisibilities>
        <default>true</default>
        <recordType>ContentVersion.ur_CaseAttachment</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <recordTypeVisibilities>
        <default>true</default>
        <personAccountDefault>true</personAccountDefault>
        <recordType>Task.ur_Task</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <tabVisibilities>
        <tab>standard-Account</tab>         
    <visibility>DefaultOn</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CmsExperiences</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-ProcessInstanceWorkitem</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Asset</tab>         
        <visibility>DefaultOn</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-BatchJob</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CMSChannel</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CapabilityDetail</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-SalesforceJourney</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Chatter</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Contact</tab>         
        <visibility>DefaultOn</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CmsAuthorHome</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-DiscoveryForAccounts</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-EmailTemplate</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-EnhancedLetterhead</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Entitlement</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-File</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-BusinessObjectivesRecommendations</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CollaborationGroup</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-AnalyticsDataManager</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-ApprovalsHome</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-JourneyHome</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-JourneyMap</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Monitor</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-ContentNote</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>       
        <tab>standard-OmniDataTransformItem</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniDataTransform</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniESignatureTemplate</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniProcessCompilation</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniProcessElement</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniProcess</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniSupervisorLightning</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniUiCard</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OmniScriptSavedSession</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-FlowOrchestrationInstance</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-FlowOrchestrationWorkItem</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-SalesExcellenceCallList</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-CommerceLandingPage</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OtherUserProfile</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-QualificationProcedure</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-DeleteEvent</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-SalesAIRelationshipGraph</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-ServiceContract</tab>         
        <visibility>DefaultOn</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-Task</tab>         
        <visibility>DefaultOn</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-VoiceCall</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    <tabVisibilities>         
        <tab>standard-OnlineSalesHome</tab>         
        <visibility>Hidden</visibility>     
    </tabVisibilities>
    
    <userLicense>Salesforce</userLicense>
    <userPermissions>
        <enabled>true</enabled>
        <name>ActivitiesAccess</name>
    </userPermissions>
    <userPermissions>
        <enabled>true</enabled>
        <name>AllowViewKnowledge</name>
    </userPermissions>
    <userPermissions>
        <enabled>true</enabled>
        <name>ApiEnabled</name>
    </userPermissions>
    <userPermissions>
        <enabled>true</enabled>
        <name>ChatterInternalUser</name>
    </userPermissions>
    <userPermissions>
        <enabled>true</enabled>
        <name>LightningConsoleAllowedForUser</name>
    </userPermissions>
    <userPermissions>
        <enabled>true</enabled>
        <name>ViewHelpLink</name>
    </userPermissions>
</Profile>
