public without sharing class NotificationHandler {
 
    public static void notificationManagement(Map<String, Object> flowParams) {
 
        Flow.Interview.NotificationSystemOrchestratorNew flowInstance = new Flow.Interview.NotificationSystemOrchestratorNew(flowParams);
        flowInstance.start();
    }
 
    public static void opportunityNotification(List<Opportunity> listUser , Map<Opportunity,Id> MapGroupId, Map<Id,List<Id>> MapUserId, String notificationName) {
 
        Map<String, Object> flowParam = new Map<String, Object>();
        List<Map<String, Object>> flowParams = new List<Map<String, Object>>();
 
        if(listUser.size() > 0){
            for(Opportunity opp : listUser){
                flowParam = new Map<String, Object>{
                            'NotificationName' => notificationName,
                            'Object' => 'Opportunity',
                            'ToWhom' => 'AssignedTo',
                            'Channel' => 'Bell',
                            'TargetRecordID' => opp.Id,
                            'RecipientID' => opp.AssignedTo__c,
                            'NotificationBodyDetails' => opp.Name
                            };
               
                flowParams.add(flowParam);
            }
        }
        system.debug( ' flowParams ' + flowParams);
       if(MapGroupId.size() > 0){
            for(Opportunity opp : MapGroupId.keySet()){
                flowParam = new Map<String, Object>{
                                    'NotificationName' => notificationName,
                                    'Object' => 'Opportunity',
                                    'ToWhom' => 'AssignedGroup',
                                    'Channel' => 'Bell',
                                    'TargetRecordID' => opp.Id,
                                    'RecipientID' => MapUserId.get(MapGroupId.get(opp)),
                                    'NotificationBodyDetails' => opp.Name
                                    };
            }
 
            flowParams.add(flowParam);
        }
        system.debug( ' lowParams.size() ' + flowParams.size());
        //The flow is created for a single record
        //if it will be modified to accept more values remove the block to 0
       
        notificationManagement(flowParams[0]);
       
    }
 
    public static void quoteNotification(List<QuoteTriggerHandler.NotificationResponseWrapper> listResult, String notificationName) {
 
        Map<String, Object> flowParam = new Map<String, Object>();
        List<Map<String, Object>> flowParams = new List<Map<String, Object>>();
 
        for(QuoteTriggerHandler.NotificationResponseWrapper  wrp : listResult){
            if(wrp.assignedTo != null){
                flowParam = new Map<String, Object>{
                    'NotificationName' => notificationName,
                    'Object' => 'Quote',
                    'ToWhom' => 'AssignedTo',
                    'Channel' => 'Bell',
                    'TargetRecordID' => wrp.id,
                    'RecipientID' => wrp.assignedTo,
                    'NotificationBodyDetails' => wrp.quoteName,
                    'NotificationBodyAdditionalDetails' => wrp.opportunityName
                };
                flowParams.add(flowParam);
            }
 
            if(wrp.assignedGroupTo != null){
                flowParam = new Map<String, Object>{
                    'NotificationName' => notificationName,
                    'Object' => 'Quote',
                    'ToWhom' => 'AssignedGroup',
                    'Channel' => 'Bell',
                    'TargetRecordID' => wrp.id,
                    'RecipientIDCollection' => wrp.assignedGroupTo,
                    'NotificationBodyDetails' => wrp.quoteName,
                    'NotificationBodyAdditionalDetails' => wrp.opportunityName
                };
                flowParams.add(flowParam);  
            }
        }
       
        if (flowParams?.size() > 0) {
            notificationManagement(flowParams[0]);
        }
    }
 
}