<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Errore_Generico</name>
        <label>Errore Generico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Si è verificato un errore: impossibile completare l&apos;operazione.
 
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Check_Case_current_status</name>
        <label>Check Case current status &amp;&amp; currentdatetime</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>In_attesa_risposta_cliente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Flow.CurrentDateTime</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>EmailMsgCreateddatePlusGiorniMancataRisp</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateCaseStatus_Ricevuta_risp_cliente</targetReference>
            </connector>
            <label>In attesa risposta cliente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Case_status_In_gestione_flag_in_attesarisposta_cliente_true</name>
        <label>Check Case status= In gestione &amp;&amp; flag inattesarisposta cliente = true</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_In_gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.InAttesaRispCliente__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateCaseStatusInattesarispcliente</targetReference>
            </connector>
            <label>In gestione</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_4</name>
        <label>Email msg is?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_4</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Incoming</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>3</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Case_status_In_gestione_flag_in_attesarisposta_cliente_true</targetReference>
            </connector>
            <label>Sent</label>
        </rules>
        <rules>
            <name>Received</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Incoming</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Case_current_status</targetReference>
            </connector>
            <label>Received</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>EmailMsgCreateddatePlusGiorniMancataRisp</name>
        <dataType>DateTime</dataType>
        <expression>{!$Record.CreatedDate} + {!$Setup.urcs_GeneralSettings__c.GiorniMancataRisposta__c}</expression>
    </formulas>
    <interviewLabel>urcs_RTEmailMessageAfterIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTEmailMessageAfterIU</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Decision_4</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Errore_Generico</targetReference>
        </faultConnector>
        <filterLogic>1 AND ( 2  OR 3 )</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In gestione</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCaseStatus_Ricevuta_risp_cliente</name>
        <label>UpdateCaseStatus: Ricevuta risp cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_Generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>NumSolleciti__c</field>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Ricevuta risposta cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateCaseStatusInattesarispcliente</name>
        <label>UpdateCaseStatus: In attesa risp cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_Generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>EmailSollecito__c</field>
            <value>
                <elementReference>$Record.ToAddress</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>dtInAttesaRispCliente__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCaseInfo</targetReference>
        </connector>
        <filterFormula>AND(
    {!$Record.ParentId} != NULL,
    OR(
      {!$Record.Parent.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
      {!$Record.Parent.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CasePQ&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseSitoWeb&apos;,
      {!$Record.Parent.RecordType.DeveloperName}  == &apos;ur_CaseES&apos;
     )
)</filterFormula>
        <object>EmailMessage</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>urRTidList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
