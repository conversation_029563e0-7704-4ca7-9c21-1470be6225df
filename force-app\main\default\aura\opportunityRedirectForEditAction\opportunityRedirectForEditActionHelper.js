({
    redirectIfNeeded : function(component) {
        var recordId = component.get("v.recordId");
        var navService = component.find("navService");
        var appContext = component.get("v.appContext");

        if (!recordId || !appContext) {
            console.log("Dati insufficienti per il redirect.");
            return;
        }

        var normalizedAppContext = appContext.trim().toLowerCase();
        var expectedApp = 'unipol crm agenzia';

        var pageReference;

        if (normalizedAppContext === expectedApp) {
            pageReference = {
                type: "standard__recordPage",
                attributes: {
                    recordId: recordId,
                    objectApiName: "Opportunity",
                    actionName: "view"
                }
            };
        } else {
            pageReference = {
                type: "standard__recordPage",
                attributes: {
                    recordId: recordId,
                    objectApiName: "Opportunity",
                    actionName: "edit"
                }
            };
        }

        navService.navigate(pageReference);
    }
})
