{"dataSourceLinksInfo": {"enableAutomaticLinking": true, "excludeRelationships": [], "links": []}, "filters": [{"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["AssegnatarioName"], "locked": false, "operator": "in"}, {"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["Origine"], "locked": false, "operator": "in"}, {"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["Product__c"], "locked": false, "operator": "in"}, {"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["AmbitoFilter"], "locked": false, "operator": "in"}, {"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["date_lavorate_formula"], "locked": false, "operator": ">=<="}, {"dataset": {"name": "Trattative_Dataset"}, "dependent": true, "fields": ["period_formula_field"], "locked": false, "operator": "in"}], "gridLayouts": [{"name": "<PERSON><PERSON><PERSON>", "numColumns": 50, "pages": [{"label": "Untitled", "name": "af9ba7f0-2950-436e-b8a0-f8cc795bff6b", "navigationHidden": false, "widgets": [{"colspan": 23, "column": 1, "name": "container_1", "row": 2, "rowspan": 38, "widgetStyle": {"borderColor": "#DDDBDA", "borderEdges": ["all"]}}, {"colspan": 23, "column": 1, "name": "text_1", "row": 2, "rowspan": 3, "widgetStyle": {"borderEdges": []}}, {"colspan": 6, "column": 2, "name": "listselector_1", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 2, "name": "listselector_2", "row": 12, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 9, "name": "listselector_5", "row": 12, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 16, "name": "listselector_6", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 21, "column": 1, "name": "chart_1", "row": 17, "rowspan": 19, "widgetStyle": {"borderEdges": []}}, {"colspan": 24, "column": 25, "name": "container_2", "row": 2, "rowspan": 38, "widgetStyle": {"borderColor": "#DDDBDA", "borderEdges": ["all"]}}, {"colspan": 22, "column": 25, "name": "text_2", "row": 2, "rowspan": 3, "widgetStyle": {"borderEdges": []}}, {"colspan": 6, "column": 26, "name": "listselector_4", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 26, "name": "listselector_3", "row": 12, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 33, "name": "listselector_7", "row": 12, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 40, "name": "listselector_8", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 21, "column": 25, "name": "chart_2", "row": 17, "rowspan": 19, "widgetStyle": {"borderEdges": []}}, {"colspan": 6, "column": 9, "name": "listselector_10", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 6, "column": 33, "name": "listselector_13", "row": 6, "rowspan": 4, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": ["all"], "borderRadius": 4, "borderWidth": 1}}, {"colspan": 5, "column": 1, "name": "link_1", "row": 37, "rowspan": 2, "widgetStyle": {"borderEdges": []}}, {"colspan": 5, "column": 25, "name": "link_2", "row": 37, "rowspan": 2, "widgetStyle": {"borderEdges": []}}]}], "rowHeight": "fine", "selectors": [], "style": {"alignmentX": "left", "alignmentY": "top", "backgroundColor": "#FFFFFF", "cellSpacingX": 0, "cellSpacingY": 0, "fit": "original", "gutterColor": "#FFFFFF"}, "version": 1.0}], "layouts": [], "steps": {"Origine_1": {"broadcastFacet": true, "groups": [], "label": "Origine_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'Origine';\nq = foreach q generate 'Origine' as 'Origine', count() as 'count';\nq = order q by 'Origine' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Product__c_1", "AssegnatarioName_1", "AmbitoFilter_2", "period_formula_field_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Origine"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "Origine_2": {"broadcastFacet": true, "groups": [], "label": "Origine_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'Origine';\nq = foreach q generate 'Origine' as 'Origine', count() as 'count';\nq = order q by 'Origine' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Product__c_2", "AmbitoFilter_2_1", "AssegnatarioName_1_1", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Origine"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AmbitoFilter_2": {"broadcastFacet": true, "groups": [], "label": "AmbitoFilter_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AssegnatarioName_1", "period_formula_field_1", "Product__c_1", "Origine_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AmbitoFilter_2_1": {"broadcastFacet": true, "groups": [], "label": "AmbitoFilter_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AssegnatarioName_1_1", "CreateDate_Formula_p_1", "Origine_2", "Product__c_2"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["AmbitoFilter"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "period_formula_field_3": {"broadcastFacet": true, "groups": [], "label": "period_formula_field_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'period_formula_field';\nq = foreach q generate 'period_formula_field' as 'period_formula_field', count() as 'count';\nq = order q by 'period_formula_field' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["period_formula_field"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "period_formula_field_2": {"broadcastFacet": true, "groups": [], "label": "period_formula_field_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'period_formula_field';\nq = foreach q generate 'period_formula_field' as 'period_formula_field', count() as 'count';\nq = order q by 'period_formula_field' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "all", "steps": []}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["period_formula_field"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AssegnatarioName_1_1": {"broadcastFacet": true, "groups": [], "label": "AssegnatarioName_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'AssegnatarioName';\nq = foreach q generate 'AssegnatarioName' as 'AssegnatarioName', count() as 'count';\nq = order q by 'AssegnatarioName' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2_1", "Product__c_2", "Origine_2", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "period_formula_field_1": {"broadcastFacet": true, "groups": [], "label": "period_formula_field_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'period_formula_field';\nq = foreach q generate 'period_formula_field' as 'period_formula_field', count() as 'count';\nq = order q by 'period_formula_field' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AssegnatarioName_1", "Product__c_1", "Origine_1", "AmbitoFilter_2"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["period_formula_field"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "CreateDate_Formula_p_1": {"broadcastFacet": true, "groups": [], "label": "CreateDate_Formula_p_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'CreateDate_Formula_period';\nq = foreach q generate 'CreateDate_Formula_period' as 'CreateDate_Formula_period', count() as 'count';\nq = order q by 'CreateDate_Formula_period' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2_1", "AssegnatarioName_1_1", "Product__c_2", "Origine_2"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["CreateDate_Formula_period"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AssegnatarioName_1": {"broadcastFacet": true, "groups": [], "label": "AssegnatarioName_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'AssegnatarioName';\nq = foreach q generate 'AssegnatarioName' as 'AssegnatarioName', count() as 'count';\nq = order q by 'AssegnatarioName' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "Product__c_1", "Origine_1", "period_formula_field_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_5": {"broadcastFacet": true, "groups": [], "label": "lens_5", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'closure_status_formula';\nq = foreach q generate q.'closure_status_formula' as 'closure_status_formula', count(q) as 'A';\nq = order q by 'closure_status_formula' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2_1", "AssegnatarioName_1_1", "Product__c_2", "Origine_2", "CreateDate_Formula_p_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": false, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": false, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": false, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}}, "Product__c_1": {"broadcastFacet": true, "groups": [], "label": "Product__c_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\", \"In gestione\"];\nq = group q by 'Product__c';\nq = foreach q generate 'Product__c' as 'Product__c', count() as 'count';\nq = order q by 'Product__c' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Origine_1", "AmbitoFilter_2", "AssegnatarioName_1", "period_formula_field_1"]}, "selectMode": "multi", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Product__c"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "Product__c_2": {"broadcastFacet": true, "groups": [], "label": "Product__c_2", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'StageName' in [\"Chiuso\"];\nq = group q by 'Product__c';\nq = foreach q generate 'Product__c' as 'Product__c', count() as 'count';\nq = order q by 'Product__c' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": ["Origine_2", "AmbitoFilter_2_1", "CreateDate_Formula_p_1", "AssegnatarioName_1_1"]}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": true, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["Product__c"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "AreaOfNeed__c_1_1": {"broadcastFacet": true, "groups": [], "label": "AreaOfNeed__c_1", "numbers": [], "query": "q = load \"Trattative_Dataset\";\nq = filter q by 'Rating__c' in [\"Calda\", \"Caldissima\", \"Tiepida\"];\nq = filter q by 'Origine' in [\"Preventivatore digitale Unica\"] && 'StageName' not in [\"Chiuso\"];\nq = group q by 'AmbitoFilter';\nq = foreach q generate 'AmbitoFilter' as 'AmbitoFilter', count() as 'count';\nq = order q by 'AmbitoFilter' asc;\nq = limit q 2000;", "receiveFacetSource": {"mode": "include", "steps": []}, "selectMode": "single", "sortable": true, "strings": [], "type": "saql", "useExternalFilters": false, "useGlobal": true, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "bins": {"breakpoints": {"high": 100, "low": 0}, "bands": {"high": {"color": "#008000", "label": ""}, "low": {"color": "#B22222", "label": ""}, "medium": {"color": "#ffa500", "label": ""}}}, "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "axisMode": "sync", "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "hbar", "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "binValues": false, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "columnMap": {"trellis": [], "dimensionAxis": ["AmbitoFilter"], "plots": ["count"]}, "showActionMenu": true, "measureAxis2": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "valueType": "compactNumber", "measureAxis1": {"sqrtScale": false, "showTitle": true, "showAxis": true, "title": "", "customDomain": {"showDomain": false}}, "theme": "wave", "compactDecimalDigits": -1, "dimensionAxis": {"showTitle": true, "customSize": "auto", "showAxis": true, "title": "", "icons": {"useIcons": false, "iconProps": {"fit": "cover", "column": "", "type": "round"}}}, "applyConditionalFormatting": true}, "type": "chart"}}, "lens_1": {"broadcastFacet": true, "datasets": [{"name": "Trattative_Dataset"}], "isGlobal": false, "label": "Torta Trattative lavorate", "query": {"aggregateFilters": [], "columnGroups": [], "columnTotals": [], "limit": 2000, "orders": [{"name": "StageName", "filters": [], "ascending": true}], "rowTotals": [], "sourceFilters": {"Trattative_Dataset": {"filters": [["StageName", ["<PERSON><PERSON><PERSON>", "In gestione"], "in"]]}}, "sources": [{"columns": [{"field": ["count", "*"], "name": "A"}], "filters": [], "groups": ["StageName"], "joins": [], "name": "Trattative_Dataset"}]}, "receiveFacetSource": {"mode": "include", "steps": ["AmbitoFilter_2", "AssegnatarioName_1", "Product__c_1", "Origine_1", "period_formula_field_1"]}, "selectMode": "single", "sortable": true, "type": "aggregateflex", "useExternalFilters": false, "useGlobal": false, "visualizationParameters": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "legend": {"descOrder": true, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": true, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "title": {"fontSize": 16, "subtitleFontSize": 11, "label": "", "align": "left", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["StageName"], "plots": ["A"]}, "showActionMenu": true, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}}}, "widgetStyle": {"backgroundColor": "#FFFFFF", "borderColor": "#747474", "borderEdges": [], "borderRadius": 0, "borderWidth": 1, "tooltipStyle": {"backgroundColor": "#16325c", "labelColor": "#9faab5", "valueColor": "#ffffff"}}, "widgets": {"listselector_7": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Product__c_2", "title": "<PERSON><PERSON><PERSON>"}, "type": "listselector"}, "listselector_6": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Origine_1", "title": "Origine"}, "type": "listselector"}, "listselector_8": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Origine_2", "title": "Origine"}, "type": "listselector"}, "listselector_3": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AmbitoFilter_2_1", "title": "Ambito"}, "type": "listselector"}, "listselector_2": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AmbitoFilter_2", "title": "Ambito"}, "type": "listselector"}, "text_1": {"parameters": {"content": {"richTextContent": [{"attributes": {"size": "12px", "bold": true}, "insert": "   Trattative Lavorate"}, {"attributes": {"align": "left"}, "insert": "\n"}]}, "interactions": [], "showActionMenu": true}, "type": "text"}, "listselector_5": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "Product__c_1", "title": "<PERSON><PERSON><PERSON>"}, "type": "listselector"}, "listselector_4": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AssegnatarioName_1_1", "title": "Assegnatar<PERSON>"}, "type": "listselector"}, "link_1": {"parameters": {"destinationLink": {"tooltipMode": "default", "url": "https://tableau-preprod.gruppounipol.cloud/#/workbooks/322/views"}, "destinationType": "url", "fontSize": 14, "includeState": false, "text": "Vai a Tableau", "textAlignment": "center", "textColor": "#0070D2"}, "type": "link"}, "link_2": {"parameters": {"destinationLink": {"tooltipMode": "default", "url": "https://tableau-preprod.gruppounipol.cloud/#/workbooks/322/views"}, "destinationType": "url", "fontSize": 14, "includeState": false, "text": "Vai a Tableau", "textAlignment": "center", "textColor": "#0070D2"}, "type": "link"}, "container_1": {"parameters": {"alignmentX": "left", "alignmentY": "top", "fit": "original", "interactions": []}, "type": "container"}, "container_2": {"parameters": {"alignmentX": "left", "alignmentY": "top", "fit": "original", "interactions": []}, "type": "container"}, "text_2": {"parameters": {"content": {"richTextContent": [{"attributes": {"size": "12px", "bold": true}, "insert": "   <PERSON><PERSON><PERSON>"}, {"attributes": {"align": "left"}, "insert": "\n"}]}, "interactions": [], "showActionMenu": true}, "type": "text"}, "listselector_1": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "AssegnatarioName_1", "title": "Assegnatar<PERSON>"}, "type": "listselector"}, "listselector_10": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "period_formula_field_1", "title": "Periodo"}, "type": "listselector"}, "chart_2": {"parameters": {"autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "centerText": "", "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": false, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "exploreLink": true, "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["closure_status_formula"], "plots": ["A"]}, "showActionMenu": false, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "step": "lens_5", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}, "chart_1": {"parameters": {"centerText": "", "autoFitMode": "<PERSON><PERSON><PERSON><PERSON>", "legend": {"descOrder": false, "showHeader": true, "show": true, "customSize": "auto", "position": "right-top", "inside": false}, "showMeasureTitle": false, "tooltip": {"content": {"legend": {"showBinLabel": true, "measures": [], "showNullValues": true, "customizeLegend": false, "showPercentage": true, "showDimensions": true, "showMeasures": true, "dimensions": []}}}, "visualizationType": "pie", "exploreLink": true, "title": {"fontSize": 14, "subtitleFontSize": 11, "label": "", "align": "center", "subtitleLabel": ""}, "trellis": {"flipLabels": false, "showGridLines": true, "size": [100, 100], "enable": false, "type": "x", "chartsPerLine": 4}, "inner": 60, "columnMap": {"trellis": [], "dimension": ["StageName"], "plots": ["A"]}, "showActionMenu": false, "centerValueType": "number", "valueType": "compactNumber", "theme": "wave", "step": "lens_1", "compactDecimalDigits": -1, "applyConditionalFormatting": true}, "type": "chart"}, "listselector_13": {"parameters": {"compact": false, "displayMode": "filter", "exploreLink": false, "filterStyle": {"titleColor": "#16325C", "valueColor": "#16325C"}, "instant": true, "interactions": [], "measureField": "count", "showActionMenu": false, "step": "CreateDate_Formula_p_1", "title": "Periodo"}, "type": "listselector"}}}