<template>
   <table class="slds-table">
    <thead style="border: 2px solid #cdcdcd;">
       <tr style="font-size: 18px; padding-top: 13px; padding-left: 13px;">
         <th scope="col">Permessi  <p style="padding: 5px;"></p>
         <p style="font-size: 14px; margin-bottom: 5px; font-weight: 500;">14 elementi di 14</p></th>
        
       </tr>
    </thead>



    <tbody>
       <template for:each={localRecords} for:item="record">
          <tr style="border: 2px solid #cdcdcd;" key={record.Id}>
             <td style="padding: 2px 2px 2px 10px  !important;">
                <lightning-input
                   type="checkbox"
                   checked={record.Checked}
                   onchange={handleChange}
                   label={record.Label}
                   data-id={record.Id}>
                </lightning-input>
             </td>
          </tr>
       </template>
    </tbody>
 </table>


  <!-- ...elenco PermissionSet con checkbox... -->
    <template if:true={showModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Esito Salvataggio</h2>
                </header>
                <div class="slds-modal__content">
                    <p>{modalMessage}</p>
                </div>
                <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_brand" onclick={handleModalOk}>OK</button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
 
</template>