<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RelatedToId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MessageDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FromName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FromAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ToAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CcAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>BccAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Message Content</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>InAttesaRispCliente__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>HtmlBody</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TextBody</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
    </platformActionList>
    <quickActionList/>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>TYPE</fields>
        <relatedList>RelatedEmailMessagePeopleList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9O00000C5mLF</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
