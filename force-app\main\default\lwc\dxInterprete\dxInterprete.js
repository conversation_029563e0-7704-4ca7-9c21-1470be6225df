import BffInterprete, {
  ACTION_LOAD_REQUEST,
  PEGA_API,
} from "c/dxBffInterprete";
import { getFormState, resetFormState } from "c/dxFormState";
import { createLogger } from "c/dxLogger";
import { utils } from "c/dxUtils";
import { utilsPega } from "c/dxUtilsPega";
import { fireEvent, registerListener, unregisterListener } from "c/pubsub";
import { LightningElement, api, track } from "lwc";

export default class Interprete extends LightningElement {
  // Lazily initialized to ensure config is ready
  get _bffInterprete() {
    return BffInterprete.getInstance(this.config);
  }

  @api request;
  @api debug;
  @api errorMessage = "";
  @api step;

  @track isLoading = false;
  @track reloadVisible = false;
  @track showModal = false;
  @track modalView = {};
  @track actionId = "";
  @track showFailurePage = false;

  view;
  emptyView = { groups: [], visible: true };
  caseData = {};

  config = {
    productType: "",
    env: "UNICO",
  };

  constructor() {
    super();
    this._logger = createLogger("dxInterprete", () => this.debug);
  }

  handlePageError(error) {
    // Check the error status code if available
    const statusCode = error?.status || error?.body?.status;
    if (statusCode === 404) {
      this.errorMessage = "La pagina richiesta non è stata trovata";
    } else if (statusCode === 503) {
      this.errorMessage = "Il servizio è momentaneamente non disponibile";
    } else if (statusCode >= 400 && statusCode < 500) {
      this.errorMessage = "Si è verificato un errore nella richiesta";
    } else if (Object.keys(error).length === 0) {
      this.errorMessage = error.codice ? `MULESOFT ERROR ${error.messaggio || "null"} \n ${error.dettaglio || "null"}` : "MULESOFT ERROR";
    } else {
      this.errorMessage = "Servizio momentaneamente non disponibile";
    }
    this.showFailurePage = true;
    if (!this.view) {
      this.view = this.emptyView;
    }
  }

  /////////////////////////////////////////////////////////////////
  ///////////////////////////// MODAL /////////////////////////////
  /////////////////////////////////////////////////////////////////

  closeModal = async ($event = {}) => {
    this.modalView = {};
    this.showModal = false;

    if (this._bffInterprete && this._bffInterprete._actionID) {
      this._bffInterprete._actionID = this.actionId;
    }

    if ($event && $event.refreshPage) {
      this.retrieveData(
        PEGA_API.loadPage,
        this._bffInterprete.requestMapper[PEGA_API.loadPage]("reload"),
        $event.target
      );
    }
  };

  async runLoadPage() {
    this.retrieveData(PEGA_API.loadPage, this.request);
  }

  async reloadPage() {
    await this.retrieveData(
      PEGA_API.loadPage,
      this._bffInterprete.requestMapper[ PEGA_API.loadPage ]("reload"),
    );
  }

  /////////////////////////////////////////////////////////////////
  ///////////////////////// EVENT HANDLERS ////////////////////////
  /////////////////////////////////////////////////////////////////

  /**
   * Generates an event handler for the provided field and parent layout.
   *
   * @param {Object} field - The field object containing control and actionSets.
   * @param {Object} parentLayout - The parent layout object (not used in the current implementation).
   * @returns {EventHandler} - The generated event handler function.
   */
  generateEventHandler(field, parentLayout) {
    const actionsList = this.getActionsList(field, parentLayout);

    const eventHandler = (evt) => {
      actionsList.reduce((promise, actionHandler) => {
        return promise.then(() =>
          actionHandler.handler(evt, actionHandler.data),
        );
      }, Promise.resolve());
    };

    return eventHandler;
  }

  handleFieldClicked(event) {
    const { evt, field, parentLayout } = event;
    this._logger.debug(
      "[TEST] handleFieldClicked",
      "\n\nEVT:\n",
      utils.printObject(evt),
      evt,
      "\n\nFIELD:\n",
      utils.printObject(field),
      "\n\nPARENTLAYOUT:\n",
      utils.printObject(parentLayout),
    );

    if (!field) return;
    const eventHandler = this.generateEventHandler(field, parentLayout);

    if (!eventHandler) return;
    eventHandler(evt);
  }

  handlerRetryClicked(request) {
    const { productType } = request;
    this.config.productType = productType;
    this.retrieveData(
      PEGA_API.loadPage,
      this._bffInterprete.requestMapper[ PEGA_API.loadPage ]("reload"),
    );
  }

  handlerDeleteValue(request) {
    if (!this.caseData || Object.keys(this.caseData).length === 0) return;
    const { field, parentLayout } = request;
    let reference = field?.reference;
    delete this.caseData[ reference ];
  }

  handleFieldChanged = (event) => {
    const { evt, field, parentLayout } = event;

    if (!evt) return;
    this._logger.debug(
      "[TEST] handleFieldChanged",
      "\n\nEVT:\n",
      utils.printObject(evt),
      "\n\nFIELD:\n",
      utils.printObject(field),
      "\n\nPARENTLAYOUT:\n",
      utils.printObject(parentLayout),
    );

    let value;
    let reference = evt?.target?.dataset?.reference || field.reference;

    if (field?.control?.type === utilsPega.fields.FIELD_TYPES.pxCheckbox) {
      value = evt?.target?.checked;
    } else if (
      field?.control?.type === utilsPega.fields.FIELD_TYPES.pxRadioButtons
    ) {
      const input = evt?.currentTarget?.querySelector?.('input[type="radio"]');
      value = evt?.target?.value || input?.value;
    } else {
      if (field?.control?.type === utilsPega.fields.FIELD_TYPES.pxDateTime) {
        value = utils.formatDateByField(evt?.target?.value, field);
      } else {
        value = evt?.target?.value;
      }
    }

    // if (this.isDate(field)) {
    //   value = value.replaceAll("-", "");
    // }

    if (!reference) return;
    if (value === this.caseData[ reference ]) return;

    this.caseData[ reference ] = String(value);

    const eventHandler = this.generateEventHandler(field, parentLayout);

    if (!eventHandler) return;
    eventHandler(evt, field);
  };

  /////////////////////////////////////////////////////////////////
  //////////////////////// ACTION HANDLERS ////////////////////////
  /////////////////////////////////////////////////////////////////

  handleLocalAction = async (evt, data) => {
    const actionData = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;
    const field = data?.field;
    const target = actionData?.target;

    this._logger.debug(
      "[TEST] handleLocalAction",
      "\n\nEVT:\n",
      utils.printObject(evt),
      evt,
      "\n\nDATA:\n",
      utils.printObject(data),
    );

    const request = this._bffInterprete.requestMapper[ PEGA_API.updatePage ]({
      actionId: actionData?.localAction,
      referencesToUpdate: {
        ...referencesToUpdate,
        "GestioneProcesso.IndiceGaranziaSelezionata": this.indexFromReference(
          field?.reference,
          "Garanzie",
        ),
      },
    });

    switch (target) {
      case "overlay":
        this.retrieveData(
          PEGA_API.updatePage,
          request,
          target,
          `overlay-response-${field.reference}`,
        );
        break;
      case "modalDialog":
        this.retrieveData(PEGA_API.updatePage, request, target);
        break;
      default:
        break;
    }
  };

  indexFromReference(reference, indexName) {
    let esito = "";
    if (reference && indexName) {
      const regex = new RegExp(`${indexName}\\((?<valore>\\S+?)\\)`);
      const execution = regex.exec(reference);
      esito = execution?.groups?.valore || "";
    }

    return esito;
  }

  handlePostValue = async (evt, data) => {
    const { actionData, referencesToUpdate, field } = data;
    const actionId = data?.actionId || this.actionId; // Usa l'actionId passato o il default
    this._logger.debug(
      "[TEST] handlePostValue",
      "\n\nEVT:\n",
      utils.printObject(evt),
      evt,
      "\n\nDATA:\n",
      utils.printObject(data),
    );

    await this.retrieveData(
      PEGA_API.updatePage,
      this._bffInterprete.requestMapper[PEGA_API.updatePage]({
        actionId: actionId, // Usa l'actionId corretto
        refreshFor: actionData?.refreshFor,
        referencesToUpdate: referencesToUpdate,
      }),
      actionData?.target,
    );

    if (actionData?.events?.[0]?.event === 'click' && field?.customAttributes?.componentID === 'closeModalAndRefreshPage') {
      this.closeModal({ refreshPage: true, target: actionData?.target });
    }
  };

  handleFinishAssignment = async (evt, data) => {
    const actionData = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;
    const actionId = data?.actionData?.actionName;
    const actionName = actionData?.actionName;

    this._logger.debug(
      "[TEST] handleFinishAssignment",
      "\n\nEVT:\n",
      utils.printObject(evt),
      evt,
      "\n\nDATA:\n",
      utils.printObject(data),
    );

    if (actionName && actionName === 'Indietro') {
      if (this.step === 0) {
        this.dispatchEvent(
          new CustomEvent("goback", {
            bubbles: true,
            composed: true,
          })
        );
        return;
      } else {
        this.step -= 2;
      }
    }

    this.retrieveData(
      PEGA_API.nextPage,
      this._bffInterprete.requestMapper[ PEGA_API.nextPage ]({
        actionId: actionId || null,
        referencesToUpdate: referencesToUpdate,
      }),
      actionData?.target,
    );
    this.step++;
  };

  handleRunDataTransform = async (evt, data) => {
    const actionInfo = data?.actionData;
    const referencesToUpdate = data?.referencesToUpdate;
    const actionIdentifier =
      referencesToUpdate?.[ "GestioneProcesso.StepSuccessivo" ] ||
      actionInfo?.actionProcess?.dataTransform;

    if (actionIdentifier === "RichiediContatto") {
      const refs = {
        AssignmentOrigine: this._bffInterprete._assignmentId,
      };

      const request = this._bffInterprete.requestMapper[ PEGA_API.loadPage ](
        ACTION_LOAD_REQUEST.create,
        {
          retrieveType: "Richiesta Contatto",
          productType: "UNICO",
          referencesToUpdate: refs,
        },
      );
      this.retrieveData(PEGA_API.loadPage, request, actionInfo?.target);
      return;
    }
    const request = this._bffInterprete.requestMapper[ PEGA_API.updatePage ]({
      actionId: actionIdentifier,
      referencesToUpdate,
    });
    this.retrieveData(PEGA_API.updatePage, request, actionInfo?.target);
  };

  handleCloseContainer = async (evt, data) => {
    const field = data?.field;
    this._logger.debug("handleCloseContainer", JSON.stringify(field));

    if (
      field.customAttributes &&
      field.customAttributes.componentID === "closeModalAndReloadPage"
    ) {
      await this.closeModal({ refreshPage: true, target: "replaceCurrent" });
    } else {
      this.closeModal();
    }
  };

  /**
   * Generates a list of actions based on the provided field and parent layout.
   *
   * @param {Object} field - The field object containing control and actionSets.
   * @param {Object} parentLayout - The parent layout object (not used in the current implementation).
   * @returns {ActionsList[]} - An array of objects, each containing a handler function and associated action
   *   data.
   */
  getActionsList = (field, parentLayout) => {
    const actionData = utilsPega.actions.getActionData(field);
    const formState = getFormState();
    console.log('getActionsList - formState', formState);
    console.log('getActionsList - field', field);

    const referencesToUpdate = {
      ...Object.fromEntries(
        Object.entries(formState.values).map(([ key, field ]) => [
          key,
          field.value,
        ]),
      ), //aggiungo tutti i value del form
      ...utilsPega.actions.getReferencesToUpdate(field),
      ...this.caseData,
      ...field.referencesToUpdate,
    };

    /** @type {ActionsList[]} */
    const actionsList = [];

    const isBoxIndirizzoIcon =
      field.control.type === "pxIcon" &&
      actionData.some(
        (a) =>
          a.action === utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM,
      ) &&
      actionData.some(
        (a) => a.action === utilsPega.actions.SUPPORTED_ACTIONS.REFRESH,
      );

    if (isBoxIndirizzoIcon) {
      this._logger.debug(
        `Rilevato caso speciale per icona BoxIndirizzo (${field.fieldID}). Eseguo solo refresh con actionId 'DettagliResidenza'.`,
      );
      const refreshAction = actionData.find(
        (a) => a.action === utilsPega.actions.SUPPORTED_ACTIONS.REFRESH,
      );
      actionsList.push({
        handler: this.handlePostValue,
        data: {
          actionId: "DettagliResidenza",
          actionData: refreshAction,
          referencesToUpdate,
          field,
        },
      });
      return actionsList; // Usciamo subito per non aggiungere altre azioni
    }
    // --- END: Gestione speciale ---

    const hasMajorAction = actionData.some((a) =>
      [
        utilsPega.actions.SUPPORTED_ACTIONS.LOCAL_ACTION,
        utilsPega.actions.SUPPORTED_ACTIONS.FINISH_ASSIGNMENT,
        utilsPega.actions.SUPPORTED_ACTIONS.PERFORM_ACTION,
      ].includes(a.action),
    );

    for (let i = 0; i < actionData.length; i++) {
      const currentAction = actionData[ i ];

      if (
        currentAction.action ===
        utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM &&
        hasMajorAction
      ) {
        this._logger.warn(
          "Azione 'runDataTransform' ignorata per evitare conflitto con un'altra azione principale.",
        );
        continue;
      }

      switch (currentAction.action) {
        case utilsPega.actions.SUPPORTED_ACTIONS.RUN_DATA_TRANSFORM:
          actionsList.push({
            handler: this.handleRunDataTransform,
            data: {
              actionData: currentAction,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.LOCAL_ACTION:
          actionsList.push({
            handler: this.handleLocalAction,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
              field,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.REFRESH:
          // The BoxIndirizzo case is handled above, this is for all other refreshes
          actionsList.push({
            handler: this.handlePostValue,
            data: {
              actionId: this.actionId, // Use default actionId
              actionData: currentAction,
              referencesToUpdate,
              field,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.FINISH_ASSIGNMENT:
        case utilsPega.actions.SUPPORTED_ACTIONS.PERFORM_ACTION:
          actionsList.push({
            handler: this.handleFinishAssignment,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
            },
          });
          break;

        case utilsPega.actions.SUPPORTED_ACTIONS.CLOSE_CONTAINER:
          actionsList.push({
            handler: this.handleCloseContainer,
            data: {
              actionData: currentAction.actionProcess,
              referencesToUpdate,
              field,
            },
          });
          break;

        default:
          break;
      }
    }

    return actionsList;
  };

  applyContextData(request){
    const referencesToUpdate = {
      "CCLocked": this.request.referencesToUpdate?.CCLocked,
      "CCLockedDate": this.request.referencesToUpdate?.CCLockedDate,
      "GestioneProcesso.UtenteLoggato": this.request.referencesToUpdate["GestioneProcesso.UtenteLoggato"],
      "GestioneProcesso.LoggedCF": this.request.referencesToUpdate["GestioneProcesso.LoggedCF"],
      "GestioneProcesso.CanaleTPD": this.request.referencesToUpdate["GestioneProcesso.CanaleTPD"],
      ...request.referencesToUpdate
    }

    return {...request, referencesToUpdate}
  }

  /**
   * @param {PegaAPI} api
   * @param {any} request
   * @param {string} [target]
   */
  retrieveData(api, request, target, eventName = "") {
    this.isLoading = true;
    this.reloadVisible = api !== PEGA_API.loadPage;

    let response;
    return this._bffInterprete
      .makeRequest(api, this.applyContextData(request), target).then((res) => {
        response = res;

        if (Object.keys(response).length === 0) {
          this.handlePageError(response);
        }
        if (target !== "modalDialog") {
          this.actionId = response?.metaBodyResponse?.actionId;
        }
        
        if (response?.pegaErrorMessages) {
          const errorMsg = response?.pegaErrorMessages?.[ 0 ];
          this.dispatchEvent(
            new CustomEvent("noresult", {
              bubbles: true,
              composed: true,
              detail: errorMsg,
            }),
          );
        }

        switch (target) {
          case "modalDialog":
            this.modalView = response?.pegaBodyResponse?.view;
            this.showModal = true;
            break;
          case "overlay":
            break;
          default:
            this.view = response?.pegaBodyResponse?.view || this.emptyView;
            window.scrollTo({ top: 0, behavior: "smooth" });
            break;
        }

        if (api === PEGA_API.nextPage) {
          //reset forma state e caseData con il next page
          resetFormState();
          this.caseData = {};
        }
      })
      .catch((error) => {
        this.handlePageError(error);
        if (!this.view) {
          this.view = this.emptyView;
        }
        this._logger.error("Error retrieving data:", utils.printObject(error));
      })
      .finally(() => {
        this.isLoading = false;
        const tooltipGroup = response?.pegaBodyResponse;
        if (eventName && tooltipGroup?.view) {
          fireEvent(eventName, tooltipGroup.view);
        }
      });
  }

  connectedCallback() {
    this.config.productType = this.request?.productType || "";
    this.runLoadPage();

    registerListener("handleFieldChanged", this.handleFieldChanged, this);
    registerListener("handleFieldClicked", this.handleFieldClicked, this);
    registerListener("handlerRetryClicked", this.handlerRetryClicked, this);
    registerListener("handlerDeleteValue", this.handlerDeleteValue, this);
    registerListener("dxLoadPage", this.runLoadPage, this);
    registerListener("dxReloadPage", this.reloadPage, this);
  }

  disconnectedCallback() {
    unregisterListener("handleFieldChanged", this.handleFieldChanged, this);
    unregisterListener("handleFieldClicked", this.handleFieldClicked, this);
    unregisterListener("handlerRetryClicked", this.handlerRetryClicked, this);
    unregisterListener("handlerDeleteValue", this.handlerDeleteValue, this);
    unregisterListener("dxLoadPage", this.runLoadPage, this);
    unregisterListener("dxReloadPage", this.reloadPage, this);
  }
}
