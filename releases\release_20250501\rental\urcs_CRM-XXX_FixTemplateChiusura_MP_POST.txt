MANUAL PROCEDURE: Fix Email Template Chiusura Case Issue
=======================================================

TICKET: urcs_CRM-XXX_FixTemplateChiusura
ENVIRONMENT: ALL
TYPE: POST-DEPLOYMENT
ESTIMATED TIME: 15 minutes

DESCRIPTION:
Fix the urcs_CaseNotificaChiusura email template that is causing errors in the 
urcs_RTCaseAfterIU flow. The template works for creation but fails during case closure.

PREREQUISITES:
- System Administrator access
- Access to Email Templates and Flows

STEPS:

1. Verify Current Template Issues
   - Navigate to Setup > Email Templates
   - Search for "urcs_CaseNotificaChiusura"
   - Open the template and note any HTML encoding issues

2. Fix HTML Encoding Issues in Template
   - In the template editor, click "Source" to view HTML
   - Replace the current HTML with the corrected version below:
   
   <html style="overflow-y: hidden;">
   <head>
       <title></title>
   </head>
   <body style="height: auto; min-height: auto;">
   <p><span style="font-family:<PERSON>,<PERSON><PERSON>,<PERSON>lve<PERSON>,sans-serif;">Gentile {{{Case.Contact}}},</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Le confermiamo che la sua richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>, è stata gestita e risolta con successo.</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
   <strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
   <strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
   <strong>Richiesta</strong>: {{{Case.Subject}}}<br />
   <strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br />
   <strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}<br />
   <br />
   La ringraziamo per aver scelto UnipolRental.<br />
   Buona giornata,<br />
   Servizio clienti UnipolRental</span></p>
   <p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa è una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
   </body>
   </html>

3. Verify Template Configuration
   - Ensure the following settings are correct:
     * Template Name: urcs_CaseNotificaChiusura
     * Related Entity Type: Case
     * Folder: urcs_EmailTemplates
     * Subject: UnipolRental - Conferma chiusura richiesta {{{Case.CaseNumber}}}
     * Enhanced Letterhead: ur_letterhead
     * Active: Checked

4. Test Template Functionality
   - Create a test case with a contact
   - Close the case to trigger the email template
   - Verify no errors occur in the flow execution

5. Check Flow Configuration (if needed)
   - Navigate to Setup > Flows
   - Open "urcs_RTCaseAfterIU" flow
   - Verify the "Get_Template_Chiusura" element correctly finds the template
   - Ensure "Copy_1_of_Send_Case_Email" element has proper error handling

VALIDATION:
- Template sends successfully without errors
- Email content displays correctly with all merge fields populated
- Flow execution completes without fault messages

ROLLBACK PLAN:
- Restore previous template HTML from backup
- Deactivate template if issues persist

NOTES:
- The main issue was HTML encoding problems with &nbsp; characters
- Removed problematic HTML entities that could cause parsing errors
- Ensured all merge fields are properly formatted with triple braces {{{field}}}

COMPLETED BY: ________________
DATE: ________________
ENVIRONMENT: ________________
