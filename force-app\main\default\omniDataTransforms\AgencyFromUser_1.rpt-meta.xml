<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AgencyFromUser</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem5</globalKey>
        <inputFieldName>Agency:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AgencyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>SFCRM</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AgencyFromUserCustom8426</globalKey>
        <inputFieldName>UserName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>UserName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>AgencyFromUserCustom1232</globalKey>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem1</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem2</globalKey>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem3</globalKey>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>compagnia</filterValue>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem4</globalKey>
        <inputFieldName>RelatedAccount_ExternalId__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>compagnia</filterValue>
        <globalKey>AgencyFromUserCustom5242</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AgencyFromUserCustom5604</globalKey>
        <inputFieldName>Agency__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:FiscalCode__c</filterValue>
        <globalKey>AgencyFromUserCustom3458</globalKey>
        <inputFieldName>FiscalCode__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;AgencySociety&apos;</filterValue>
        <globalKey>AgencyFromUserCustom3360</globalKey>
        <inputFieldName>RecordType.Name</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:User:FirstName &quot;/\/\/&quot; var:User:LastName CONCAT</formulaConverted>
        <formulaExpression>CONCAT(User:FirstName, &quot; &quot; , User:LastName)</formulaExpression>
        <formulaResultPath>UserName</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AgencyFromUserCustom1371</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>userId</filterValue>
        <globalKey>AgencyFromUserCustom0jI9V000000r9htUAAItem0</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;TRUE&apos;</filterValue>
        <globalKey>AgencyFromUserCustom7131</globalKey>
        <inputFieldName>IsActive__c</inputFieldName>
        <inputObjectName>NetworkUser__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>NetworkUser</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>SFCRM</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AgencyFromUserCustom2529</globalKey>
        <inputFieldName>NetworkUser:NetworkUser__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AgencyFromUser</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>UserId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;userId&quot; : &quot;0059X00000PrV0GQAV&quot;,
  &quot;compagnia&quot; : &quot;SOC_1&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>AgencyFromUser_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
