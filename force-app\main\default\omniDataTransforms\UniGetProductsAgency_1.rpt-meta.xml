<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description>Modified by <PERSON></description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;GetPolicies&quot; : null,
    &quot;GetUserDetails&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniGetProductsAgency</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | | var:InsurancePolicies LIST &apos;InsurancePolicy.Agency__c/\/\/==/\/\/&quot;&apos; var:CurrentUser:IdAzienda__c + &apos;&quot;&apos; + FILTER LIST</formulaConverted>
        <formulaExpression>LIST(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.Agency__c == &quot;&apos; + CurrentUser:IdAzienda__c + &apos;&quot;&apos;))</formulaExpression>
        <formulaResultPath>sameUserAgencyPolicies_temp</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:CasaFamigliaPoliciesAgency|1 var:null == | var:CasaFamigliaPoliciesAgency LISTSIZE 1 - | var:CasaFamigliaPoliciesAgency LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(CasaFamigliaPoliciesAgency|1 == null, LISTSIZE(CasaFamigliaPoliciesAgency) - 1, LISTSIZE(CasaFamigliaPoliciesAgency))</formulaExpression>
        <formulaResultPath>CountOfCasaFamigliaPoliciesAgency</formulaResultPath>
        <formulaSequence>16.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>3b6b6b63-8480-4b63-8093-8b3ed7407b1f</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.RecordType.DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:rtDeveloperName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | | var:InsurancePolicies LIST &apos;InsurancePolicy.Agency__c/\/\/!=/\/\/&quot;&apos; var:CurrentUser:IdAzienda__c + &apos;&quot;&apos; + FILTER LIST</formulaConverted>
        <formulaExpression>LIST(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.Agency__c != &quot;&apos; + CurrentUser:IdAzienda__c + &apos;&quot;&apos;))</formulaExpression>
        <formulaResultPath>otherAgencyPolicies_temp</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem42</globalKey>
        <inputFieldName>HasUnicaPolicies</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>has_unica_policies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem32</globalKey>
        <inputFieldName>CountOfMotorPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CountOfMotorPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem41</globalKey>
        <inputFieldName>showOtherAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>showOtherAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem9</globalKey>
        <inputFieldName>PrimaryParticipantAccountId</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:showOtherAgency false == | | | var:sameUserAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Casa/\/\/e/\/\/Famiglia&quot;/\/\/||/\/\/InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Altri/\/\/Prodotti&quot;&apos; FILTER LIST | | | var:otherAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Casa/\/\/e/\/\/Famiglia&quot;/\/\/||/\/\/InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Altri/\/\/Prodotti&quot;&apos; FILTER LIST IF</formulaConverted>
        <formulaExpression>IF(
  showOtherAgency == false,
  LIST(FILTER(LIST(sameUserAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Casa e Famiglia&quot; || InsurancePolicy.CommercialFamily__c == &quot;Altri Prodotti&quot;&apos;)),
  LIST(FILTER(LIST(otherAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Casa e Famiglia&quot; || InsurancePolicy.CommercialFamily__c == &quot;Altri Prodotti&quot;&apos;))
)</formulaExpression>
        <formulaResultPath>CasaFamigliaPoliciesAgency</formulaResultPath>
        <formulaSequence>15.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem12</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:CasaFamigliaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c SUM</formulaConverted>
        <formulaExpression>SUM(CasaFamigliaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c)</formulaExpression>
        <formulaResultPath>SumOfCasaFamigliaPoliciesAgency</formulaResultPath>
        <formulaSequence>17.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem14</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>Folder</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem0</globalKey>
        <inputFieldName>InsurancePolicy.RecordType.DeveloperName</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem39</globalKey>
        <inputFieldName>SumOfPersonaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SumOfPersonaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem38</globalKey>
        <inputFieldName>SumOfMotorPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SumOfMotorPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem40</globalKey>
        <inputFieldName>SumOfSalutePoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SumOfSalutePoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem7</globalKey>
        <inputFieldName>PrimaryParticipantAccountId</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:SalutePoliciesAgency|1 var:null == | var:SalutePoliciesAgency LISTSIZE 1 - | var:SalutePoliciesAgency LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(SalutePoliciesAgency|1 == null, LISTSIZE(SalutePoliciesAgency) - 1, LISTSIZE(SalutePoliciesAgency))</formulaExpression>
        <formulaResultPath>CountOfSalutePoliciesAgency</formulaResultPath>
        <formulaSequence>22.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem19</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PersonaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c SUM</formulaConverted>
        <formulaExpression>SUM(PersonaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c)</formulaExpression>
        <formulaResultPath>SumOfPersonaPoliciesAgency</formulaResultPath>
        <formulaSequence>20.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem17</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:otherAgencyPolicies|1 var:null == | var:otherAgencyPolicies LISTSIZE 1 - | var:otherAgencyPolicies LISTSIZE ( IF</formulaConverted>
        <formulaExpression>IF(otherAgencyPolicies|1 == null, LISTSIZE(otherAgencyPolicies) - 1, LISTSIZE(otherAgencyPolicies)</formulaExpression>
        <formulaResultPath>countOfOtherAgencyPolicies</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:sameUserAgencyPolicies_temp|1 var:null == var:sameUserAgencyPolicies_temp|1 | var:sameUserAgencyPolicies_temp LIST IF</formulaConverted>
        <formulaExpression>IF(sameUserAgencyPolicies_temp|1 == null, sameUserAgencyPolicies_temp|1, LIST(sameUserAgencyPolicies_temp))</formulaExpression>
        <formulaResultPath>sameUserAgencyPolicies</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>Folder</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem8</globalKey>
        <inputFieldName>InsurancePolicy.RecordType.DeveloperName</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PersonaPoliciesAgency|1 var:null == | var:PersonaPoliciesAgency LISTSIZE 1 - | var:PersonaPoliciesAgency LISTSIZE ( IF</formulaConverted>
        <formulaExpression>IF(PersonaPoliciesAgency|1 == null, LISTSIZE(PersonaPoliciesAgency) - 1, LISTSIZE(PersonaPoliciesAgency)</formulaExpression>
        <formulaResultPath>CountOfPersonaPoliciesAgency</formulaResultPath>
        <formulaSequence>19.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem16</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:showOtherAgency false == | | | var:sameUserAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Motor&quot;&apos; FILTER LIST | | | var:otherAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Motor&quot;&apos; FILTER LIST IF</formulaConverted>
        <formulaExpression>IF(
  showOtherAgency == false,
  LIST(FILTER(LIST(sameUserAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Motor&quot;&apos;)),
  LIST(FILTER(LIST(otherAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Motor&quot;&apos;))
)</formulaExpression>
        <formulaResultPath>MotorPoliciesAgency</formulaResultPath>
        <formulaSequence>9.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem24</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:showOtherAgency false == | | | var:sameUserAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Salute&quot;/\/\/||/\/\/InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Unisalute/\/\/per/\/\/Te&quot;&apos; FILTER LIST | | | var:otherAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Salute&quot;/\/\/||/\/\/InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Unisalute/\/\/per/\/\/Te&quot;&apos; FILTER LIST IF</formulaConverted>
        <formulaExpression>IF(
  showOtherAgency == false,
  LIST(FILTER(LIST(sameUserAgencyPolicies),&apos;InsurancePolicy.CommercialFamily__c == &quot;Salute&quot; || InsurancePolicy.CommercialFamily__c == &quot;Unisalute per Te&quot;&apos;)),
  LIST(FILTER(LIST(otherAgencyPolicies),&apos;InsurancePolicy.CommercialFamily__c == &quot;Salute&quot; || InsurancePolicy.CommercialFamily__c == &quot;Unisalute per Te&quot;&apos;))
)</formulaExpression>
        <formulaResultPath>SalutePoliciesAgency</formulaResultPath>
        <formulaSequence>21.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem18</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:otherAgencyPolicies_temp|1 var:null == var:otherAgencyPolicies_temp|1 | var:otherAgencyPolicies_temp LIST IF</formulaConverted>
        <formulaExpression>IF(otherAgencyPolicies_temp|1 == null, otherAgencyPolicies_temp|1, LIST(otherAgencyPolicies_temp))</formulaExpression>
        <formulaResultPath>otherAgencyPolicies</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem10</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&quot;&quot;</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem11</globalKey>
        <inputFieldName>InsurancePolicy.ActiveDate__c</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:showOtherAgency false == | | | var:sameUserAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Vita&quot;&apos; FILTER LIST | | | var:otherAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Vita&quot;&apos; FILTER LIST IF</formulaConverted>
        <formulaExpression>IF(
  showOtherAgency == false,
  LIST(FILTER(LIST(sameUserAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Vita&quot;&apos;)),
  LIST(FILTER(LIST(otherAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Vita&quot;&apos;))
)</formulaExpression>
        <formulaResultPath>VitaPoliciesAgency</formulaResultPath>
        <formulaSequence>12.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem26</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>ORDER BY</filterOperator>
        <filterValue>InsurancePolicy.CommercialFamily__c</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem22</globalKey>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>&gt;</filterOperator>
        <filterValue>Date</filterValue>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem21</globalKey>
        <inputFieldName>InsurancePolicy.ActiveDate__c</inputFieldName>
        <inputObjectName>InsurancePolicyParticipant</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>InsurancePolicies</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:showOtherAgency false == | | | var:sameUserAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Persona&quot;&apos; FILTER LIST | | | var:otherAgencyPolicies LIST &apos;InsurancePolicy.CommercialFamily__c/\/\/==/\/\/&quot;Persona&quot;&apos; FILTER LIST IF</formulaConverted>
        <formulaExpression>IF(
  showOtherAgency == false,
  LIST(FILTER(LIST(sameUserAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Persona&quot;&apos;)),
  LIST(FILTER(LIST(otherAgencyPolicies), &apos;InsurancePolicy.CommercialFamily__c == &quot;Persona&quot;&apos;))
)</formulaExpression>
        <formulaResultPath>PersonaPoliciesAgency</formulaResultPath>
        <formulaSequence>18.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:MotorPoliciesAgency|1 var:null == | var:MotorPoliciesAgency LISTSIZE 1 - | var:MotorPoliciesAgency LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(MotorPoliciesAgency|1 == null, LISTSIZE(MotorPoliciesAgency) - 1, LISTSIZE(MotorPoliciesAgency))</formulaExpression>
        <formulaResultPath>CountOfMotorPoliciesAgency</formulaResultPath>
        <formulaSequence>10.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem23</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:SalutePoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c SUM</formulaConverted>
        <formulaExpression>SUM(SalutePoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c)</formulaExpression>
        <formulaResultPath>SumOfSalutePoliciesAgency</formulaResultPath>
        <formulaSequence>23.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem20</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:MotorPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c SUM</formulaConverted>
        <formulaExpression>SUM(MotorPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c)</formulaExpression>
        <formulaResultPath>SumOfMotorPoliciesAgency</formulaResultPath>
        <formulaSequence>11.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem28</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:countOfPolicies 0 &gt; | | | var:InsurancePolicies LIST &apos;InsurancePolicy.RecordType.DeveloperName/\/\/LIKE/\/\/&quot;PU&quot;&apos; FILTER LISTSIZE 0 &gt; &amp;&amp;</formulaConverted>
        <formulaExpression>countOfPolicies &gt; 0 &amp;&amp; LISTSIZE(FILTER(LIST(InsurancePolicies), &apos;InsurancePolicy.RecordType.DeveloperName LIKE &quot;PU&quot;&apos;)) &gt; 0</formulaExpression>
        <formulaResultPath>HasUnicaPolicies</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem25</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem31</globalKey>
        <inputFieldName>CountOfCasaFamigliaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CountOfCasaFamigliaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:VitaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c SUM</formulaConverted>
        <formulaExpression>SUM(VitaPoliciesAgency:InsurancePolicy.NPI__r.GrossWrittenPremium__c)</formulaExpression>
        <formulaResultPath>SumOfVitaPoliciesAgency</formulaResultPath>
        <formulaSequence>14.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem29</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem30</globalKey>
        <inputFieldName>InsurancePolicies:InsurancePolicy.Agency__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>policies:agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem35</globalKey>
        <inputFieldName>CountOfVitaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CountOfVitaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem36</globalKey>
        <inputFieldName>SumOfCasaFamigliaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SumOfCasaFamigliaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem33</globalKey>
        <inputFieldName>CountOfPersonaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CountOfPersonaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem34</globalKey>
        <inputFieldName>CountOfSalutePoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CountOfSalutePoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:VitaPoliciesAgency|1 var:null == | var:VitaPoliciesAgency LISTSIZE 1 - | var:VitaPoliciesAgency LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(VitaPoliciesAgency|1 == null, LISTSIZE(VitaPoliciesAgency) - 1, LISTSIZE(VitaPoliciesAgency))</formulaExpression>
        <formulaResultPath>CountOfVitaPoliciesAgency</formulaResultPath>
        <formulaSequence>13.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem27</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem37</globalKey>
        <inputFieldName>SumOfVitaPoliciesAgency</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SumOfVitaPoliciesAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:countOfSameUserAgencyPolicies var:countOfOtherAgencyPolicies SUM</formulaConverted>
        <formulaExpression>SUM(countOfSameUserAgencyPolicies, countOfOtherAgencyPolicies)</formulaExpression>
        <formulaResultPath>countOfPolicies</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem6</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:sameUserAgencyPolicies|1 var:null == | var:sameUserAgencyPolicies LISTSIZE 1 - | var:sameUserAgencyPolicies LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(sameUserAgencyPolicies|1 == null, LISTSIZE(sameUserAgencyPolicies) - 1, LISTSIZE(sameUserAgencyPolicies))</formulaExpression>
        <formulaResultPath>countOfSameUserAgencyPolicies</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>UniGetProductsAgencyCustom0jI9O000000uxmzUAAItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetProductsAgency</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X00001HoznNQAR&quot;,
  &quot;Folder&quot; : &quot;PU_FOLDER&quot;,
  &quot;showOtherAgency&quot; : &quot;TRUE&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetProductsAgency_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
