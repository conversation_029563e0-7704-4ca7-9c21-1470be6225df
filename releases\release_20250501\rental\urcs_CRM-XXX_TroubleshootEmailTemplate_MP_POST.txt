MANUAL PROCEDURE: Troubleshoot Email Template Sending Issues
============================================================

TICKET: urcs_CRM-XXX_TroubleshootEmailTemplate
ENVIRONMENT: ALL
TYPE: POST-DEPLOYMENT
ESTIMATED TIME: 30 minutes

DESCRIPTION:
Troubleshoot and fix urcs_CaseNotificaCreazione and urcs_CaseNotificaChiusura email templates 
that are failing to send with generic Salesforce errors.

INVESTIGATION APPROACH:
Since formula fields are populated correctly, the issue may be related to:
- HTML encoding problems
- OrgWideEmailAddress configuration  
- Contact email delivery issues
- Template configuration problems

PREREQUISITES:
- System Administrator access
- Access to Email Templates and Setup

TROUBLESHOOTING STEPS:

1. Create Test Template (FIRST STEP)
   - Navigate to Setup > Email Templates
   - Click "New Email Template"
   - Set Template Name: urcs_TestTemplate
   - Set Related Entity Type: Case
   - Set Folder: urcs_EmailTemplates
   - Set Subject: Test - {{{Case.CaseNumber}}}
   - Set Enhanced Letterhead: ur_letterhead
   - In HTML Value, use this minimal content:
   
   <html>
   <body>
   <p>Test email for case {{{Case.CaseNumber}}}</p>
   <p>Contact: {{{Case.Contact}}}</p>
   <p>Subject: {{{Case.Subject}}}</p>
   </body>
   </html>
   
   - Save and activate the template

2. Test Simple Template
   - Temporarily modify the flow urcs_RTCaseAfterIU
   - Change Get_Email_Template_Creazione to search for "urcs_TestTemplate"
   - Test case creation to see if simple template works
   - If it works, the problem is in the original template HTML

3. Check OrgWideEmailAddress Configuration
   - Navigate to Setup > Organization-Wide Addresses
   - Find "Unipol Rental CS No Reply"
   - Verify it's verified and active
   - Check if there are any delivery restrictions

4. Check Contact Email Deliverability
   - Open the failing contact record
   - Check if Email Opt Out is checked
   - Check if there are any email bounce records
   - Verify the email address format is valid

5. If Simple Template Works - Fix Original Templates
   - The issue is in the HTML content
   - Try this cleaned version for urcs_CaseNotificaCreazione:
   
   <html>
   <head><title></title></head>
   <body style="height: auto; min-height: auto;">
   <p><span style="font-family:Arial,sans-serif;">Gentile {{{Case.Contact}}},</span></p>
   <p><span style="font-family:Arial,sans-serif;">le confermiamo di aver ricevuto la richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>.</span></p>
   <p><span style="font-family:Arial,sans-serif;">Di seguito il riepilogo:<br/>
   <strong>Categoria</strong>: {{{Case.Categoria__c}}}<br/>
   <strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br/>
   <strong>Richiesta</strong>: {{{Case.Subject}}}<br/>
   <strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br/>
   <strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}</span></p>
   <p><span style="font-family:Arial,sans-serif;">Il nostro team sta analizzando la sua richiesta e la contatterà al più presto.</span></p>
   <p><span style="font-family:Arial,sans-serif;">La ringraziamo per aver scelto UnipolRental.<br/>
   Buona giornata,<br/>
   Servizio clienti UnipolRental</span></p>
   <p><span style="font-family:Arial,sans-serif;"><em>Questa è una mail automatica inviata da un indirizzo no-reply.</em></span></p>
   </body>
   </html>

6. Apply Same Fix to Closure Template
   - Use similar cleaned HTML for urcs_CaseNotificaChiusura
   - Replace "ricevuto la richiesta" with "gestita e risolta con successo"
   - Remove the middle paragraph about team analysis

7. Test Both Templates
   - Create a test case
   - Verify creation email sends
   - Close the case
   - Verify closure email sends

VALIDATION:
- Test template sends successfully
- Original templates send successfully after cleanup
- All merge fields display correctly
- No flow errors occur

ROLLBACK PLAN:
- Delete test template
- Restore original template HTML from backup
- Revert flow changes if made

NOTES:
- The issue appears to be HTML encoding or syntax related
- Simplified font-family and removed complex styling
- Removed potential problematic characters
- If problem persists, check Salesforce email limits and deliverability settings

COMPLETED BY: ________________
DATE: ________________
ENVIRONMENT: ________________
